import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:go_router/go_router.dart';
import '../../../core/config/app_strings.dart' as app_strings;
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/navigation/web_router.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/utils/validators.dart';
import '../../cubit/auth/cubit/auth_cubit.dart';
import '../../shared/components/app_textfield.dart';
import '../../shared/components/elevated_button.dart';

class CreatePasswordScreen extends HookWidget {
  final String? inviteId;
  final String? email;
  final String? userId;

  CreatePasswordScreen({super.key, this.inviteId, this.email, this.userId});

  ValueNotifier<bool> isLoading = ValueNotifier(false);

  @override
  Widget build(BuildContext context) {
    final formKey = useMemoized(() => GlobalKey<FormState>());
    final emailController = useTextEditingController();
    final passwordController = useTextEditingController();
    final confirmPasswordController = useTextEditingController();

    // Password visibility state
    final isPasswordObscured = useState(true);
    final isConfirmPasswordObscured = useState(true);

    // Initialize email field if email is provided
    useEffect(() {
      if (email != null && email!.isNotEmpty) {
        emailController.text = email!;
      }
      return null;
    }, [email]);
    final isMobile = Responsive.isMobile(context);

    return SingleChildScrollView(
      child: Container(
        margin: const EdgeInsets.all(defaultPadding),
        width: isMobile ? double.infinity : 550,
        decoration: BoxDecoration(
          color: AppTheme.roundIconColor,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ValueListenableBuilder(
          valueListenable: isLoading,
          builder: (context, loadingState, child) {
            return Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(25),

                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Header
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                          vertical: defaultPadding * 2.2,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.roundIconColor,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(25),
                            topRight: Radius.circular(25),
                          ),
                        ),
                        child: Center(
                          child: Text(
                            app_strings.resetYourLoginCredentials,
                            style: AppFonts.semiBoldTextStyle(
                              18,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),

                      // Form
                      Container(
                        decoration: BoxDecoration(
                          color: AppTheme.white,
                          borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(25),
                            topRight: Radius.circular(25),
                          ),
                        ),
                        padding: const EdgeInsets.all(defaultPadding * 1.5),
                        child: Form(
                          key: formKey,
                          child: Column(
                            children: [
                              // Username (email)
                              Align(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  app_strings.username,
                                  style: AppFonts.regularTextStyle(
                                    14,
                                    color: AppTheme.primaryTextColor,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 8),
                              AppTextField(
                                controller: emailController,
                                hintText: app_strings.enterEmailAddress,
                                keyboardType: TextInputType.emailAddress,
                                validator: (value) =>
                                    InputValidators.validateEmail(value),
                                isMobile: isMobile,
                                enable: false,
                              ),
                              const SizedBox(height: defaultPadding),

                              // Password
                              Align(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  app_strings.password,
                                  style: AppFonts.regularTextStyle(
                                    14,
                                    color: AppTheme.primaryTextColor,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 8),
                              AppTextField(
                                controller: passwordController,
                                hintText: app_strings.enterPassword,
                                isObscure: isPasswordObscured.value,
                                showToggle: true,
                                onToggleObscure: () {
                                  isPasswordObscured.value =
                                      !isPasswordObscured.value;
                                },
                                validator: (value) =>
                                    InputValidators.validatePassword(value),
                                isMobile: isMobile,
                              ),
                              const SizedBox(height: defaultPadding),

                              // Confirm Password
                              Align(
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  app_strings.confirmPassword,
                                  style: AppFonts.regularTextStyle(
                                    14,
                                    color: AppTheme.primaryTextColor,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 8),
                              AppTextField(
                                controller: confirmPasswordController,
                                hintText: app_strings.reEnterPassword,
                                isObscure: isConfirmPasswordObscured.value,
                                showToggle: true,
                                onToggleObscure: () {
                                  isConfirmPasswordObscured.value =
                                      !isConfirmPasswordObscured.value;
                                },
                                validator: (value) =>
                                    InputValidators.validateConfirmPassword(
                                      value,
                                      passwordController.text,
                                    ),
                                isMobile: isMobile,
                              ),
                              const SizedBox(height: defaultPadding * 2),

                              // Buttons
                              _buildBtnView(
                                formKey,
                                emailController,
                                passwordController,
                                confirmPasswordController,
                                context,
                              ),
                              const SizedBox(height: defaultPadding),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                isLoading.value
                    ? Positioned.fill(
                        child: Container(
                          color: Colors.transparent,
                          child: Center(child: CircularProgressIndicator()),
                        ),
                      )
                    : const SizedBox(),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildBtnView(
    GlobalKey<FormState> formKey,
    TextEditingController emailController,
    TextEditingController passwordController,
    TextEditingController confirmPasswordController,
    BuildContext context,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AppButton(
          label: app_strings.clear,
          backgroundColor: AppTheme.scaffoldBgColor,
          foregroundColor: AppTheme.primaryTextColor,
          borderRadius: 25,
          padding: EdgeInsets.symmetric(
            horizontal: defaultPadding * 2,
            vertical: defaultPadding * 1.2,
          ),
          useMinSize: true,
          onPressed: () {
            emailController.clear();
            passwordController.clear();
            confirmPasswordController.clear();
            formKey.currentState?.reset();
          },
        ),
        const SizedBox(width: defaultPadding),
        AppButton(
          label: app_strings.proceed,
          backgroundColor: AppTheme.roundIconColor,
          foregroundColor: Colors.white,
          borderRadius: 25,
          padding: const EdgeInsets.symmetric(
            horizontal: defaultPadding * 2,
            vertical: defaultPadding * 1.2,
          ),
          useMinSize: true,
          onPressed: () async {
            if (formKey.currentState!.validate()) {
              isLoading.value = true;
              // TODO: Handle reset logic
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(SnackBar(content: Text(app_strings.proceeding)));

              await _handlePasswordReset(
                context,
                emailController,
                passwordController,
                confirmPasswordController,
              );
            }
          },
        ),
      ],
    );
  }

  _handlePasswordReset(
    BuildContext context,
    TextEditingController emailController,
    TextEditingController passwordController,
    TextEditingController confirmPasswordController,
  ) async {
    final authCubit = context.read<AuthCubit>();
    final userIdToPass = userId ?? '';
    await authCubit.createPassword(
      userIdToPass,
      passwordController.text.trim(),
      confirmPasswordController.text.trim(),
    );

    final state = authCubit.state;
    if (state is PasswordResetSuccess) {
      final snackBar = SnackBar(
        content: Text(app_strings.passwordResetSuccess),
        duration: Duration(seconds: 2),
        backgroundColor: Colors.green,
      );

      // Show snackbar and wait until it's closed
      await ScaffoldMessenger.of(context).showSnackBar(snackBar).closed;
      isLoading.value = false;
      context.go(AppRoutes.login.path);
    } else if (state is PasswordResetFailure) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(state.error)));
      isLoading.value = false;
    }
  }
}
