import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../../core/config/app_strings.dart' as AppStrings;
import '../../../../core/config/constants.dart';
import '../../../../core/config/responsive.dart';
import '../../../../core/theme/app_fonts.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/utils/dotted_line_painter.dart';
import '../../../../domain/models/agent.dart';
import '../../../../domain/models/broker.dart';
import 'agent_network_card.dart';

class UpstreamUsersSection extends HookWidget {
  final List<Agent> upstreamUsers;
  final Broker? broker;

  const UpstreamUsersSection({
    super.key,
    required this.upstreamUsers,
    required this.broker,
  });

  @override
  Widget build(BuildContext context) {
    final isExpanded = useState<bool>(false);
    final size = MediaQuery.of(context).size;
    final isTablet = Responsive.isTablet(context);
    final isMobile = Responsive.isMobile(context);
    final isSmallMobile = Responsive.isSmallMobile(context);

    if (upstreamUsers.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        _buildUpstreamSection(
          context,
          size,
          isExpanded,
          isTablet,
          isMobile,
          isSmallMobile,
        ),
        _networkLine(),
      ],
    );
  }

  Widget _buildUpstreamSection(
    BuildContext context,
    Size size,
    ValueNotifier<bool> isExpanded,
    bool isTablet,
    bool isMobile,
    bool isSmallMobile,
  ) {
    final upstreamLength = upstreamUsers.length;

    // Scenario 1: More than 2 users - show collapsed/expanded view
    if (upstreamLength > 2) {
      return ValueListenableBuilder<bool>(
        valueListenable: isExpanded,
        builder: (context, expanded, _) {
          if (expanded) {
            return _buildExpandedView(
              context,
              size,
              isExpanded,
              isTablet,
              isMobile,
              isSmallMobile,
            );
          } else {
            return _buildCollapsedView(
              context,
              size,
              isExpanded,
              isTablet,
              isMobile,
              isSmallMobile,
            );
          }
        },
      );
    }
    // Scenario 2 & 3: 1 or 2 users - show all directly
    else {
      return _buildDirectView(context, size, isTablet, isMobile, isSmallMobile);
    }
  }

  Widget _buildCollapsedView(
    BuildContext context,
    Size size,
    ValueNotifier<bool> isExpanded,
    bool isTablet,
    bool isMobile,
    bool isSmallMobile,
  ) {
    final rootParent = upstreamUsers.first;
    final immediateParent = upstreamUsers.last;
    final hiddenCount = upstreamUsers.length - 2;

    return Column(
      children: [
        // Root parent card
        _buildAgentCard(
          context: context,
          agent: rootParent,
          size: size,
          isTablet: isTablet,
          isMobile: isMobile,
          isSmallMobile: isSmallMobile,
        ),
        _dottedLine(),

        // Expandable circle with count
        _buildExpandableCircle(context, hiddenCount, isExpanded),
        _dottedLine(),

        // Immediate parent card
        _buildAgentCard(
          context: context,
          agent: immediateParent,
          size: size,
          isTablet: isTablet,
          isMobile: isMobile,
          isSmallMobile: isSmallMobile,
        ),
      ],
    );
  }

  Widget _buildExpandedView(
    BuildContext context,
    Size size,
    ValueNotifier<bool> isExpanded,
    bool isTablet,
    bool isMobile,
    bool isSmallMobile,
  ) {
    return Column(
      children: [
        // Show all upstream users
        for (int i = 0; i < upstreamUsers.length; i++) ...[
          _buildAgentCard(
            context: context,
            agent: upstreamUsers[i],
            size: size,
            isTablet: isTablet,
            isMobile: isMobile,
            isSmallMobile: isSmallMobile,
          ),
          if (i < upstreamUsers.length - 1) _solidLine(),
        ],

        // Minimize button
        const SizedBox(height: 8),
        _buildMinimizeButton(context, isExpanded),
        const SizedBox(height: 8),
      ],
    );
  }

  Widget _buildDirectView(
    BuildContext context,
    Size size,
    bool isTablet,
    bool isMobile,
    bool isSmallMobile,
  ) {
    return Column(
      children: [
        for (int i = 0; i < upstreamUsers.length; i++) ...[
          _buildAgentCard(
            context: context,
            agent: upstreamUsers[i],
            size: size,
            isTablet: isTablet,
            isMobile: isMobile,
            isSmallMobile: isSmallMobile,
          ),
          if (i < upstreamUsers.length - 1) _solidLine(),
        ],
      ],
    );
  }

  Widget _buildAgentCard({
    required BuildContext context,
    required Agent agent,
    required Size size,
    required bool isTablet,
    required bool isMobile,
    required bool isSmallMobile,
  }) {
    return SizedBox(
      width: isTablet
          ? size.width / 2.5
          : isMobile
          ? isSmallMobile
                ? size.width
                : size.width / 1.8
          : size.width / 4,
      height: 100,
      child: AgentNetworkCard(
        broker: broker,
        agent: agent,
        isMainCard: false,
        isBrokerCard: false,
      ),
    );
  }

  Widget _buildExpandableCircle(
    BuildContext context,
    int hiddenCount,
    ValueNotifier<bool> isExpanded,
  ) {
    return Tooltip(
      message: AppStrings.viewAllUpstreamAgents,
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: GestureDetector(
          onTap: () => isExpanded.value = true,
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: AppTheme.primaryColor,
              border: Border.all(color: AppTheme.borderColor, width: 2),
            ),
            child: Center(
              child: Text(
                '$hiddenCount',
                style: AppFonts.semiBoldTextStyle(14, color: AppTheme.white),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMinimizeButton(
    BuildContext context,
    ValueNotifier<bool> isExpanded,
  ) {
    return GestureDetector(
      onTap: () => isExpanded.value = false,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: AppTheme.primaryColor,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Material(
          child: InkWell(
            mouseCursor: SystemMouseCursors.click,
            hoverColor: Colors.transparent,
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.keyboard_arrow_up, color: AppTheme.white, size: 16),
                const SizedBox(width: 4),
                Text(
                  AppStrings.minimize,
                  style: AppFonts.mediumTextStyle(12, color: AppTheme.white),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _dottedLine() {
    return const DottedLine(
      height: 30,
      width: 1,
      color: AppTheme.hierarchyLineColor,
      dashWidth: 3,
      dashSpace: 3,
      direction: Axis.vertical,
    );
  }

  Widget _solidLine() {
    return Container(height: 30, width: 1, color: AppTheme.hierarchyLineColor);
  }

  Widget _networkLine() {
    return Container(height: 30, width: 1, color: AppTheme.hierarchyLineColor);
  }
}
