import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import '../../../core/config/app_strings.dart';
import '../../../core/config/responsive.dart';
import '../../../core/theme/app_fonts.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/config/constants.dart';

class SignupLayoutScreen extends HookWidget {
  final Widget? child;
  const SignupLayoutScreen({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Scaffold(
      backgroundColor: AppTheme.scaffoldBgColor,
      body: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage('$imageAssetpath/register_bg.png'),
            fit: BoxFit.fill,
          ),
        ),
        child: Center(
          child: SingleChildScrollView(
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: Responsive.isMobile(context)
                    ? size.height * 0.8
                    : size.height * 0.6,
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: defaultPadding * 2,
                ),
                child: Column(
                  children: [
                    Text(
                      appName,
                      style: AppFonts.boldTextStyle(32, color: AppTheme.black),
                    ),
                    const SizedBox(height: defaultPadding * 2),
                    child ?? Container(),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
