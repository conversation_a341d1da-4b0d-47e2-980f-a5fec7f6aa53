import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:file_picker/file_picker.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import '../../cubit/agent/agent_cubit.dart';
import '/src/core/navigation/web_router.dart';
import '/src/core/utils/regex.dart';
import 'dart:io';
import '../../../core/network/api_consts.dart';
import '../../../core/utils/whitespace_formatter.dart';
import '../../../domain/models/RegisteredAgent.dart';
import '/src/presentation/cubit/broker/broker_cubit.dart';
import '/src/presentation/cubit/user/user_cubit.dart';
import '../../../core/config/app_strings.dart';
import '../../shared/components/app_textfield.dart';
import '../../../core/utils/validators.dart';
import '../../../core/utils/dotted_line_painter.dart';
import '../../shared/components/elevated_button.dart';
import '../../../core/config/app_strings.dart' as AppStrings;
import '../../../core/config/constants.dart';
import '../../../core/config/responsive.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/app_fonts.dart';

class AgentRegistrationScreen extends HookWidget {
  final String? inviteId;
  final bool isSignUp;
  AgentRegistrationScreen({super.key, this.inviteId, this.isSignUp = false});

  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController cityController = TextEditingController();
  final TextEditingController stateController = TextEditingController();
  final TextEditingController postalCodeController = TextEditingController();
  final TextEditingController countryController = TextEditingController();
  final TextEditingController agentLicenseIdController =
      TextEditingController();
  final TextEditingController additionalInfoController =
      TextEditingController();
  final TextEditingController referralCodeController = TextEditingController();

  // Controllers for invite field
  final TextEditingController firstNameControllerInvite =
      TextEditingController();
  final TextEditingController lastNameControllerInvite =
      TextEditingController();
  final TextEditingController phoneControllerInvite = TextEditingController();
  final TextEditingController emailControllerInvite = TextEditingController();

  final selectedIndex = ValueNotifier(0);
  final _formKey = GlobalKey<FormState>();

  final ValueNotifier<PlatformFile?> agentLicenseFile = ValueNotifier(null);
  final ValueNotifier<bool> showFileUploadError = ValueNotifier(false);
  final ValueNotifier<bool> hasUploadedFiles = ValueNotifier(false);
  String recruiterIdForSignup = '';
  final int maxFileSizeInBytes = 25 * 1024 * 1024;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isMobile = Responsive.isMobile(context);
    // form should be reset/cleared when reopened,
    // regardless of whether the form was previously filled and not submitted.
    // if want to persist the data remove the useEffect
    useEffect(() {
      _clearForm();
      return null;
    }, []);

    useEffect(() {
      if (isSignUp && inviteId != null) {
        context.read<AgentCubit>().getRegisteredAgentInfo(inviteId!);
      }
      return null;
    });

    return _formWidget(context, size, isMobile);
  }

  _fillFormWithRegisteredAgentInfo(RegisteredAgent? agent) {
    firstNameController.text = agent?.firstName ?? '';
    lastNameController.text = agent?.lastName ?? '';
    emailController.text = agent?.email ?? '';
    phoneController.text = agent?.phone ?? '';
    cityController.text = agent?.city ?? '';
    stateController.text = agent?.state ?? '';
    postalCodeController.text = agent?.zipCode ?? '';
    countryController.text = agent?.country ?? '';
    agentLicenseIdController.text = agent?.licenseNumber ?? '';
    additionalInfoController.text = agent?.additionalInfo ?? '';
    referralCodeController.text = agent?.inviteCode ?? '';
    String? uploadedFile = agent?.uploadedDocuments?.firstOrNull;
    if (uploadedFile != null) {
      agentLicenseFile.value = PlatformFile(
        name: uploadedFile,
        path: uploadedFile,
        size: 0,
      );
    }
    hasUploadedFiles.value = uploadedFile != null;
    recruiterIdForSignup = agent?.recruiterId ?? '';
  }

  Widget _formWidget(BuildContext context, Size size, bool isMobile) {
    return ValueListenableBuilder(
      valueListenable: selectedIndex,
      builder: (context, value, child) {
        return BlocConsumer<AgentCubit, AgentState>(
          listener: (context, state) {},
          builder: (context, state) {
            if (state is AgentRegisteredInfoLoaded) {
              _fillFormWithRegisteredAgentInfo(state.registeredAgent);
            }
            return Container(
              decoration: BoxDecoration(color: AppTheme.scaffoldBgColor),
              child: Center(
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      constraints: BoxConstraints(
                        maxWidth: isMobile ? double.infinity : 600,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(25),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 10,
                            offset: Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _formHeader(context),
                          _formContent(isMobile, context),
                        ],
                      ),
                    ),

                    state is AgentLoading
                        ? Positioned.fill(
                            child: Container(
                              color: Colors.transparent,
                              child: Center(child: CircularProgressIndicator()),
                            ),
                          )
                        : const SizedBox(),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  Container _formHeader(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(
        horizontal: defaultPadding * 2,
        vertical: defaultPadding * 1.5,
      ),
      decoration: BoxDecoration(
        color: AppTheme.roundIconColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(25),
          topRight: Radius.circular(25),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: defaultPadding * 1.2),
          Text(
            isSignUp ? AppStrings.signupAgent : AppStrings.registerAgent,
            style: AppFonts.semiBoldTextStyle(22, color: Colors.white),
          ),
          const SizedBox(height: defaultPadding * 1.2),
        ],
      ),
    );
  }

  Widget _registerTab(
    BuildContext context,
    String label,
    bool isActive,
    int index,
  ) {
    return Expanded(
      child: Container(
        margin: EdgeInsets.all(2),
        child: AppButton(
          label: label,
          backgroundColor: isActive ? AppTheme.roundIconColor : Colors.white,
          foregroundColor: Colors.white,
          elevation: 0,
          borderSide: BorderSide.none,
          borderRadius: 50,
          padding: EdgeInsets.symmetric(vertical: 12),
          textStyle: AppFonts.mediumTextStyle(
            14,
            color: isActive ? Colors.white : AppTheme.roundIconColor,
          ),
          onPressed: () {
            _clearForm();
            selectedIndex.value = index;
          },
        ),
      ),
    );
  }

  Container _formContent(bool isMobile, BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(defaultPadding * 2),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(25),
          bottomRight: Radius.circular(25),
        ),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                AppStrings.agentInformation,
                style: AppFonts.semiBoldTextStyle(
                  18,
                  color: AppTheme.primaryTextColor,
                ),
              ),
            ),
            SizedBox(height: defaultPadding * 1.5),

            _buildFormFields(isMobile),

            SizedBox(height: defaultPadding * 2),
            _actionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildFormFields(bool isMobile) {
    bool isRegisterTab = selectedIndex.value == 0;

    if (isMobile) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildFormLabel(AppStrings.firstName, isMandatory: true),
          const SizedBox(height: 8),
          _buildTextFormField(
            isRegisterTab ? firstNameController : firstNameControllerInvite,
            AppStrings.enterFirstName,
            AppStrings.firstName,
            isMandatory: true,
            validator: (value) => InputValidators.validateTextLengthRange(
              value,
              fieldLabel: AppStrings.firstName,
              limit: 50,
            ),
          ),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.lastName, isMandatory: true),
          const SizedBox(height: 8),
          _buildTextFormField(
            isRegisterTab ? lastNameController : lastNameControllerInvite,
            AppStrings.enterLastName,
            AppStrings.lastName,
            isMandatory: true,
            validator: (value) => InputValidators.validateTextLengthRange(
              value,
              fieldLabel: AppStrings.lastName,
              limit: 50,
            ),
          ),
          const SizedBox(height: defaultPadding),
          _buildFormLabel(AppStrings.phone, isMandatory: true),
          const SizedBox(height: 8),
          _phoneNumTextFormField(
            isRegisterTab ? phoneController : phoneControllerInvite,
          ),
          const SizedBox(height: defaultPadding),
          _buildFormLabel(AppStrings.email, isMandatory: true),
          const SizedBox(height: 8),
          _emailTextFormField(
            isRegisterTab ? emailController : emailControllerInvite,
          ),
          const SizedBox(height: defaultPadding),
          if (isRegisterTab) ..._buildRegisterFields(true),
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFormLabel(AppStrings.firstName, isMandatory: true),
                    const SizedBox(height: 8),
                    _buildTextFormField(
                      firstNameController,
                      AppStrings.enterFirstName,
                      AppStrings.firstName,
                      isMandatory: true,
                      validator: (value) =>
                          InputValidators.validateTextLengthRange(
                            value,
                            fieldLabel: AppStrings.firstName,
                            limit: 50,
                          ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: defaultPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildFormLabel(AppStrings.lastName, isMandatory: true),
                    const SizedBox(height: 8),
                    _buildTextFormField(
                      lastNameController,
                      AppStrings.enterLastName,
                      AppStrings.lastName,
                      isMandatory: true,
                      validator: (value) =>
                          InputValidators.validateTextLengthRange(
                            value,
                            fieldLabel: AppStrings.lastName,
                            limit: 50,
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.phone, isMandatory: true),
          const SizedBox(height: 8),
          _phoneNumTextFormField(
            isRegisterTab ? phoneController : phoneControllerInvite,
          ),
          const SizedBox(height: defaultPadding),

          _buildFormLabel(AppStrings.email, isMandatory: true),
          const SizedBox(height: 8),
          _emailTextFormField(
            isRegisterTab ? emailController : emailControllerInvite,
          ),
          const SizedBox(height: defaultPadding),

          if (isRegisterTab) ..._buildRegisterFields(false),
        ],
      );
    }
  }

  List<Widget> _buildRegisterFields(bool isMobile) {
    if (isMobile) {
      return [
        _buildFormLabel(AppStrings.city, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          cityController,
          AppStrings.enterCity,
          AppStrings.city,
          validator: (value) => InputValidators.validateTextLengthRange(
            value,
            fieldLabel: AppStrings.city,
            limit: 100,
          ),
          isMandatory: true,
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.stateProvince, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          stateController,
          AppStrings.enterState,
          AppStrings.stateProvince,
          validator: (value) => InputValidators.validateTextLengthRange(
            value,
            fieldLabel: AppStrings.stateProvince,
            limit: 100,
          ),
          isMandatory: true,
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.postalZipCode, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          postalCodeController,
          AppStrings.postalCodeEg,
          AppStrings.postalZipCode,
          validator: (value) => InputValidators.validateZipCode(value),
          isMandatory: true,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9-]')),
          ],
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.country, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          countryController,
          AppStrings.enterCountry,
          AppStrings.country,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return "${AppStrings.country} is required";
            }
            // Regex: only letters, spaces, hyphen, apostrophe
            final regex = RegExp(r"^[a-zA-Z\s'-]+$");
            if (!regex.hasMatch(value.trim())) {
              return AppStrings.enterValidCountryName;
            }
            // Extra length validation
            return InputValidators.validateTextLengthRange(
              value,
              fieldLabel: AppStrings.country,
              limit: 56,
            );
          },
          isMandatory: true,
          inputFormatters: [FilteringTextInputFormatter.deny(RegExp(r'[0-9]'))],
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.agentLicenseId, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          agentLicenseIdController,
          AppStrings.enterAgentLicenseId,
          AppStrings.agentLicenseId,
          isMandatory: true,
        ),
        const SizedBox(height: defaultPadding),

        if (isSignUp) ...[
          _buildFormLabel(AppStrings.referralCode, isMandatory: true),
          const SizedBox(height: 8),
          _buildTextFormField(
            referralCodeController,
            AppStrings.enterReferralCode,
            AppStrings.referralCode,
            isMandatory: true,
          ),
          const SizedBox(height: defaultPadding),
        ],

        _buildFormLabel(AppStrings.uploadAgentLicenseId, isMandatory: false),
        const SizedBox(height: 8),
        _buildUploadColumn(),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.additionalInformation),
        const SizedBox(height: 8),
        _buildTextFormField(additionalInfoController, '', '', maxLines: 4),
      ];
    } else {
      return [
        _textFormFieldRowWidget(
          AppStrings.city,
          cityController,
          AppStrings.stateProvince,
          stateController,
          validatorValueLeftController: (value) =>
              InputValidators.validateTextLengthRange(
                value,
                fieldLabel: AppStrings.city,
                limit: 100,
              ),
          validatorValueRightController: (value) =>
              InputValidators.validateTextLengthRange(
                value,
                fieldLabel: AppStrings.stateProvince,
                limit: 100,
              ),
        ),
        const SizedBox(height: defaultPadding),

        _textFormFieldRowWidget(
          AppStrings.postalZipCode,
          postalCodeController,
          AppStrings.country,
          countryController,
          validatorValueLeftController: InputValidators.validateZipCode,
          validatorValueRightController: (value) {
            if (value == null || value.trim().isEmpty) {
              return "${AppStrings.country} is required";
            }
            // Regex: only letters, spaces, hyphen, apostrophe
            final regex = RegExp(r"^[a-zA-Z\s'-]+$");
            if (!regex.hasMatch(value.trim())) {
              return AppStrings.enterValidCountryName;
            }
            // Extra length validation
            return InputValidators.validateTextLengthRange(
              value,
              fieldLabel: AppStrings.country,
              limit: 56,
            );
          },
        ),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.agentLicenseId, isMandatory: true),
        const SizedBox(height: 8),
        _buildTextFormField(
          agentLicenseIdController,
          AppStrings.enterAgentLicenseId,
          AppStrings.agentLicenseId,
          isMandatory: true,
          validator: (value) => InputValidators.validateTextLengthRange(
            value,
            fieldLabel: AppStrings.agentLicenseId,
            limit: 20,
          ),
        ),
        const SizedBox(height: defaultPadding),

        if (isSignUp) ...[
          _buildFormLabel(AppStrings.referralCode, isMandatory: true),
          const SizedBox(height: 8),
          _buildTextFormField(
            referralCodeController,
            AppStrings.enterReferralCode,
            AppStrings.referralCode,
            isMandatory: true,
          ),
          const SizedBox(height: defaultPadding),
        ],

        _buildFormLabel(AppStrings.uploadAgentLicenseId, isMandatory: false),
        const SizedBox(height: 8),
        _buildUploadColumn(),
        const SizedBox(height: defaultPadding),

        _buildFormLabel(AppStrings.additionalInformation),
        const SizedBox(height: 8),
        _buildTextFormField(additionalInfoController, '', '', maxLines: 4),
      ];
    }
  }

  Column _buildUploadColumn() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildUploadField(
          AppStrings.chooseFileOrDragDrop,
          AppStrings.pdfOrImageOnly,
          agentLicenseFile,
          APIConsts.allowedFileExtensions,
        ),
        _fileUploadTxt(),
      ],
    );
  }

  ValueListenableBuilder<bool> _fileUploadTxt() {
    return ValueListenableBuilder<bool>(
      valueListenable: showFileUploadError,
      builder: (context, hasError, child) {
        if (hasError) {
          return Padding(
            padding: const EdgeInsets.only(top: 8.0, left: 16.0),
            child: Text(
              AppStrings.pleaseUploadLicence,
              style: AppFonts.regularTextStyle(12, color: Colors.red),
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _textFormFieldRowWidget(
    String leftLabel,
    TextEditingController leftController,
    String rightLabel,
    TextEditingController rightController, {
    String? Function(String?)? validatorValueLeftController,
    String? Function(String?)? validatorValueRightController,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(leftLabel, isMandatory: true),
              const SizedBox(height: 8),
              _buildTextFormField(
                leftController,
                (leftController == cityController)
                    ? AppStrings.enterCity
                    : (leftController == postalCodeController)
                    ? postalCodeEg
                    : '',
                // (leftController == postalCodeController) ? postalCodeEg : '',
                leftLabel,
                validator: validatorValueLeftController,
                isMandatory: true,
                inputFormatters: (leftController == postalCodeController)
                    ? [FilteringTextInputFormatter.allow(RegExp(r'[0-9-]'))]
                    : [],
              ),
            ],
          ),
        ),
        const SizedBox(width: defaultPadding),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildFormLabel(rightLabel, isMandatory: true),
              const SizedBox(height: 8),
              _buildTextFormField(
                rightController,
                (rightController == stateController)
                    ? AppStrings.enterState
                    : (rightController == countryController)
                    ? AppStrings.enterCountry
                    : '',
                rightLabel,
                validator: validatorValueRightController,
                isMandatory: true,
                inputFormatters: (rightController == countryController)
                    ? [FilteringTextInputFormatter.deny(RegExp(r'[0-9]'))]
                    : [],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _actionButtons(BuildContext context) {
    final isSmallMobile = Responsive.isSmallMobile(context);

    if (isSmallMobile) {
      return Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: AppButton(
              label: selectedIndex.value == 0
                  ? AppStrings.register
                  : AppStrings.invite,
              backgroundColor: AppTheme.roundIconColor,
              foregroundColor: Colors.white,
              borderRadius: 25,
              padding: EdgeInsets.symmetric(
                horizontal: defaultPadding * 2,
                vertical: defaultPadding * 0.75,
              ),
              onPressed: () async => isSignUp
                  ? await _submitSignupForm(context)
                  : await _submitForm(context),
            ),
          ),
          const SizedBox(height: defaultPadding),
          SizedBox(
            width: double.infinity,
            child: AppButton(
              label: AppStrings.clear,
              backgroundColor: AppTheme.scaffoldBgColor,
              foregroundColor: AppTheme.primaryTextColor,
              borderRadius: 25,
              padding: EdgeInsets.symmetric(
                horizontal: defaultPadding * 2.5,
                vertical: defaultPadding * 0.75,
              ),
              onPressed: () => _showClearDataAlert(context),
            ),
          ),
        ],
      );
    } else {
      // Keep horizontal layout for regular mobile, tablet, and desktop
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AppButton(
            label: AppStrings.clear,
            backgroundColor: AppTheme.scaffoldBgColor,
            foregroundColor: AppTheme.primaryTextColor,
            borderRadius: 25,
            padding: const EdgeInsets.symmetric(
              horizontal: defaultPadding * 2.5,
              vertical: defaultPadding / 2,
            ),
            onPressed: () => _showClearDataAlert(context),
          ),
          const SizedBox(width: defaultPadding),
          AppButton(
            label: selectedIndex.value == 0
                ? AppStrings.register
                : AppStrings.invite,
            backgroundColor: AppTheme.roundIconColor,
            foregroundColor: Colors.white,
            borderRadius: 25,
            padding: const EdgeInsets.symmetric(
              horizontal: defaultPadding * 2,
              vertical: defaultPadding / 2,
            ),
            onPressed: () async => isSignUp
                ? await _submitSignupForm(context)
                : await _submitForm(context),
          ),
        ],
      );
    }
  }

  Widget _buildFormLabel(String label, {bool isMandatory = false}) {
    return Align(
      alignment: Alignment.centerLeft,
      child: RichText(
        text: TextSpan(
          text: label,
          style: AppFonts.regularTextStyle(
            14,
            color: AppTheme.primaryTextColor,
          ),
          children: isMandatory
              ? [
                  TextSpan(
                    text: ' *',
                    style: AppFonts.regularTextStyle(
                      14,
                      color: AppTheme.textFieldMandatoryColor,
                    ),
                  ),
                ]
              : [],
        ),
      ),
    );
  }

  Widget _buildTextFormField(
    TextEditingController controller,
    String hintText,
    String fieldLabel, {
    bool isMandatory = false,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    int maxLines = 1,
  }) {
    // Create base formatters list
    List<TextInputFormatter> finalFormatters = [];

    if (controller != phoneController ||
        controller != emailController ||
        controller != postalCodeController) {
      finalFormatters.add(WhitespaceFormatter());
    }

    // Add any additional formatters passed as parameter
    if (inputFormatters != null) {
      finalFormatters.addAll(inputFormatters);
    }

    return AppTextField(
      controller: controller,
      hintText: hintText,
      isMandatory: isMandatory,
      // validator: validator,
      validator: (value) {
        if (isMandatory) {
          if (value == null || value.isEmpty) {
            // Use fieldLabel instead of hintText
            return '$fieldLabel is required';
          } else if (value.trim().isEmpty) {
            return AppStrings.whiteSpaceValidation;
          }
        }
        if (validator != null) {
          return validator(value);
        }
        return null;
      },
      keyboardType: keyboardType,
      inputFormatters: finalFormatters,
      isMobile: false,
    );
  }

  Widget _emailTextFormField(TextEditingController controller) {
    return _buildTextFormField(
      controller,
      AppStrings.enterEmail,
      AppStrings.email,
      isMandatory: true,
      keyboardType: TextInputType.emailAddress,
      validator: (value) => InputValidators.validateEmail(value),
    );
  }

  Widget _phoneNumTextFormField(TextEditingController controller) {
    return _buildTextFormField(
      controller,
      AppStrings.enterPhone,
      AppStrings.phone,
      isMandatory: true,
      keyboardType: TextInputType.phone,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(10),
      ],
      validator: (value) => InputValidators.validatePhone(value),
    );
  }

  Widget _buildUploadField(
    String hintText,
    String formatText,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) {
    // if (isSignUp && hasUploadedFiles.value) {
    //   return const SizedBox.shrink();
    // }

    return ValueListenableBuilder<bool>(
      valueListenable: showFileUploadError,
      builder: (context, hasError, child) {
        return ValueListenableBuilder<PlatformFile?>(
          valueListenable: fileNotifier,
          builder: (context, file, child) {
            final isSmallMobile = Responsive.isSmallMobile(context);

            // Determine border color based on validation state
            Color borderColor = Colors.grey;
            if (file != null) {
              borderColor = Colors.green.shade200;
            } else if (hasError) {
              borderColor = Colors.red;
            }

            return buildDottedBorderContainerWithRadius(
              borderRadius: 25.0,
              borderColor: borderColor,
              child: Container(
                width: double.infinity,
                height: isSmallMobile ? 100 : 120,
                padding: EdgeInsets.all(
                  isSmallMobile ? defaultPadding / 2 : defaultPadding,
                ),
                decoration: BoxDecoration(
                  color: file != null
                      ? Colors.green.shade50
                      : hasError
                      ? Colors.red.shade50
                      : AppTheme.docUploadBgColor,
                  borderRadius: BorderRadius.circular(25),
                  border: file != null
                      ? Border.all(color: Colors.green.shade200)
                      : hasError
                      ? Border.all(color: Colors.red.shade200)
                      : null,
                ),
                child: file != null
                    ? _buildFileUploadedView(isSmallMobile, file, fileNotifier)
                    : _buildUploadFileView(
                        context,
                        fileNotifier,
                        allowedExtensions,
                        isSmallMobile,
                        hintText,
                        formatText,
                      ),
              ),
            );
          },
        );
      },
    );
  }

  Stack _buildFileUploadedView(
    bool isSmallMobile,
    PlatformFile file,
    ValueNotifier<PlatformFile?> fileNotifier,
  ) {
    return Stack(
      children: [
        Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green,
                size: isSmallMobile ? 16 : 20,
              ),
              SizedBox(width: isSmallMobile ? 8 : 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      file.name,
                      style: AppFonts.mediumTextStyle(
                        isSmallMobile ? 12 : 14,
                        color: Colors.green.shade700,
                      ),
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${(file.size / 1024).toStringAsFixed(1)} ${AppStrings.fileSizeKB}',
                      style: AppFonts.regularTextStyle(
                        isSmallMobile ? 10 : 12,
                        color: Colors.green.shade600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Positioned(
          top: 0,
          right: 0,
          child: GestureDetector(
            onTap: () {
              fileNotifier.value = null;
              // Reset validation error when file is removed
              showFileUploadError.value = false;
              hasUploadedFiles.value = false;
            },
            child: Icon(
              Icons.close,
              color: Colors.red,
              size: isSmallMobile ? 16 : 20,
            ),
          ),
        ),
      ],
    );
  }

  Column _buildUploadFileView(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
    bool isSmallMobile,
    String hintText,
    String formatText,
  ) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ElevatedButton.icon(
          onPressed: () =>
              _showFilePickerOptions(context, fileNotifier, allowedExtensions),
          icon: Image.asset(
            '$iconAssetpath/upload.png',
            height: isSmallMobile ? 14 : 16,
            width: isSmallMobile ? 14 : 16,
          ),
          label: Text(
            AppStrings.upload,
            style: AppFonts.mediumTextStyle(
              isSmallMobile ? 12 : 14,
              color: AppTheme.black,
            ),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.white,
            foregroundColor: AppTheme.primaryTextColor,
            elevation: 0,
            padding: EdgeInsets.symmetric(
              horizontal: isSmallMobile ? 8 : 12,
              vertical: isSmallMobile ? 4 : 8,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(color: AppTheme.borderColor),
            ),
          ),
        ),
        SizedBox(height: isSmallMobile ? 4 : 8),
        Text(
          hintText,
          textAlign: TextAlign.center,
          style: AppFonts.mediumTextStyle(
            isSmallMobile ? 10 : 12,
            color: AppTheme.black,
          ),
        ),
        Text(
          formatText,
          textAlign: TextAlign.center,
          style: AppFonts.regularTextStyle(
            isSmallMobile ? 9 : 12,
            color: AppTheme.ternaryTextColor,
          ),
        ),
      ],
    );
  }

  /// Show file picker options for iOS compatibility
  Future<void> _showFilePickerOptions(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) async {
    if (kIsWeb) {
      // On web, directly use file picker
      return _pickFile(context, fileNotifier, allowedExtensions);
    }

    // On mobile, show options
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text(AppStrings.photoLibrary),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromGallery(context, fileNotifier);
                },
              ),
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text(AppStrings.camera),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromCamera(context, fileNotifier);
                },
              ),
              ListTile(
                leading: const Icon(Icons.folder),
                title: const Text(AppStrings.files),
                onTap: () {
                  Navigator.pop(context);
                  _pickFile(context, fileNotifier, allowedExtensions);
                },
              ),
              ListTile(
                leading: const Icon(Icons.cancel),
                title: const Text(AppStrings.cancel),
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Pick image from gallery using image_picker
  Future<void> _pickFromGallery(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image != null) {
        // Convert XFile to PlatformFile
        final file = File(image.path);
        final bytes = await file.readAsBytes();

        final platformFile = PlatformFile(
          name: image.name,
          size: bytes.length,
          path: image.path,
          bytes: bytes,
        );

        fileNotifier.value = platformFile;
        showFileUploadError.value = false;
      }
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
            '${AppStrings.failedToPickImageFromGallery}: ${e.toString()}',
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Pick image from camera using image_picker
  Future<void> _pickFromCamera(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
      );

      if (image != null) {
        // Convert XFile to PlatformFile
        final file = File(image.path);
        final bytes = await file.readAsBytes();

        final platformFile = PlatformFile(
          name: image.name,
          size: bytes.length,
          path: image.path,
          bytes: bytes,
        );

        fileNotifier.value = platformFile;
        showFileUploadError.value = false;
      }
    } catch (e) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('${AppStrings.failedToCaptureImage}: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _pickFile(
    BuildContext context,
    ValueNotifier<PlatformFile?> fileNotifier,
    List<String> allowedExtensions,
  ) async {
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    try {
      FilePickerResult? result;
      if (kIsWeb) {
        // Web-specific configuration
        result = await FilePicker.platform.pickFiles(
          type: FileType.custom,
          allowedExtensions: allowedExtensions,
          allowMultiple: false,
          withData: true,
        );
      } else {
        // Mobile/Desktop configuration - try different approaches for iOS
        try {
          result = await FilePicker.platform.pickFiles(
            type: FileType.custom,
            allowedExtensions: allowedExtensions,
            allowMultiple: false,
            withData: false,
          );
        } catch (e) {
          debugPrint('Custom file type failed, trying FileType.any: $e');
          // Fallback to any file type if custom fails on iOS
          result = await FilePicker.platform.pickFiles(
            type: FileType.any,
            allowMultiple: false,
            withData: false,
            compressionQuality: 80,
          );
        }
      }

      debugPrint('File picker result: ${result?.files.length ?? 0} files');

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        debugPrint(
          'Selected file: ${file.name}, size: ${file.size}, extension: ${file.extension}',
        );

        // File size validation
        if (file.size > maxFileSizeInBytes) {
          final fileSizeInMB = (file.size / (1024 * 1024)).toStringAsFixed(2);
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(
                'File size exceeds maximum limit of 25 MB (Selected file: $fileSizeInMB MB)',
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
              margin: const EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          );
          return;
        }
        // Validate file type for mobile (since we use FileType.any)
        if (!kIsWeb) {
          final extension = file.extension?.toLowerCase();
          if (extension == null || !allowedExtensions.contains(extension)) {
            debugPrint(
              'Invalid file type: $extension. Allowed: $allowedExtensions',
            );
            scaffoldMessenger.showSnackBar(
              SnackBar(
                content: Text(
                  '${AppStrings.pleaseSelectValidFileType} ${allowedExtensions.join(', ')}',
                ),
                backgroundColor: Colors.red,
              ),
            );
            return;
          }
        }

        // Validate file based on platform
        if (kIsWeb) {
          if (file.bytes != null) {
            fileNotifier.value = file;
            // Clear validation error when file is selected
            showFileUploadError.value = false;
          } else {
            debugPrint('Web: File bytes not available');
          }
        } else {
          if (file.path != null) {
            fileNotifier.value = file;
            // Clear validation error when file is selected
            showFileUploadError.value = false;
          } else {
            debugPrint('Mobile: File path not available');
          }
        }
      } else {
        debugPrint('No file selected or result is null');
      }
    } catch (e) {
      debugPrint('$errorPickingFile: $e');
      // Show user-friendly error message
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('$failedToOpenFilePicker: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Signup after clinking on link or
  /// normal signup from login screens
  _submitSignupForm(BuildContext context) async {
    if (!context.mounted) return;
    if (!_formKey.currentState!.validate()) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text(AppStrings.pleaseFillRequiredFields)),
      );
      return;
    }
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final agentCubit = context.read<AgentCubit>();
    final user = context.read<UserCubit>().state;

    final payload = _buildAgentPayload(user);

    await agentCubit.signupAgent(payload);
    final state = agentCubit.state;

    if (state is AgentSignupSuccess) {
      if (agentLicenseFile.value == null || agentLicenseFile.value!.size <= 0) {
        final snackBar = SnackBar(
          content: Text(
            isSignUp
                ? agentRegisteredSuccessfully
                : AppStrings.agentCreatedSuccessfully,
          ),
          duration: Duration(seconds: 2),
          backgroundColor: Colors.green,
        );

        // Show snackbar and wait until it's closed
        await scaffoldMessenger.showSnackBar(snackBar).closed;
        context.go(
          AppRoutes.createPassword.path,
          extra: {'email': emailController.text.trim(), 'userId': state.userId},
        );

        return;
      }

      await _handleFileUpload(
        agentCubit: agentCubit,
        scaffoldMessenger: scaffoldMessenger,
        file: agentLicenseFile.value,
        userId: state.userId,
      );

      context.go(
        AppRoutes.createPassword.path,
        extra: {'email': emailController.text.trim(), 'userId': state.userId},
      );
    } else if (state is AgentSignupError) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(state.error)));
    }
  }

  /// Agent registration/invitation
  Future<void> _submitForm(BuildContext context) async {
    if (selectedIndex.value == 1) {
      //invite tab
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Under development')));
      return;
    }
    final user = context.read<UserCubit>().state;
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final agentCubit = context.read<AgentCubit>();

    // 1️⃣ Validate form
    if (!_formKey.currentState!.validate()) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text(AppStrings.pleaseFillRequiredFields),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
    // TODO: Remove after regitration edit feature implemented at api side
    // 2️⃣ Check license file: not mandatory
    // if (agentLicenseFile.value == null) {
    //   showFileUploadError.value = true;
    //   scaffoldMessenger.showSnackBar(
    //     const SnackBar(content: Text(AppStrings.pleaseUploadLicence)),
    //   );
    //   return;
    // }
    // showFileUploadError.value = false;

    // 3️⃣ Build payload
    final payload = _buildAgentPayload(user);
    // 4️⃣ Register agent
    await agentCubit.resgiterAgent(payload);
    final state = agentCubit.state;

    if (state is AgentCreated) {
      await _handleFileUpload(
        agentCubit: agentCubit,
        scaffoldMessenger: scaffoldMessenger,
        file: agentLicenseFile.value,
        userId: state.userId,
      );
    } else if (state is AgentLoaded) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          backgroundColor: Colors.green,
          content: Text(AppStrings.agentCreatedSuccessfully),
        ),
      );
    } else if (state is AgentError) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text('${AppStrings.failedToCreateAgent}: ${state.message}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Map<String, dynamic> _buildAgentPayload(UserState user) {
    Map<String, dynamic> payload = {
      "recruiterId": isSignUp ? recruiterIdForSignup : user.user?.userId.trim(),
      "firstName": firstNameController.text.trim(),
      "lastName": lastNameController.text.trim(),
      "email": emailController.text.trim(),
      "phone": phoneController.text,
      "city": cityController.text.trim(),
      "state": stateController.text.trim(),
      "postalCode": postalCodeController.text.trim(),
      "country": countryController.text.trim(),
      "agentLicenseId": agentLicenseIdController.text.trim(),
      "additionalInfo": additionalInfoController.text.trim(),
    };

    if (isSignUp) {
      payload.addAll({
        "inviteCode": referralCodeController.text.trim(),
        "inviteId": inviteId,
      });
      payload.remove("recruiterId");
    }
    return payload;
  }

  Future<void> _handleFileUpload({
    required AgentCubit agentCubit,
    required ScaffoldMessengerState scaffoldMessenger,
    required PlatformFile? file,
    required String? userId,
  }) async {
    if (file == null) {
      if (!isSignUp) {
        _clearForm();
      }
      scaffoldMessenger.showSnackBar(
        SnackBar(
          backgroundColor: Colors.green,
          content: Text(
            isSignUp
                ? agentRegisteredSuccessfully
                : AppStrings.agentCreatedSuccessfully,
          ),
        ),
      );
      return;
    }

    final isValidFile = kIsWeb ? file.bytes != null : file.path != null;
    if (!isValidFile) {
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text(AppStrings.invalidFile),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final uploadFilePayload = {
      "userId": isSignUp ? userId : '',
      "invitedUserId": isSignUp ? '' : userId,
      "categoryType": APIConsts.agentCategoryType,
      "documentType": APIConsts.agentDocType,
      "file": file,
    };

    await agentCubit.uploadAgentFile(uploadFilePayload);
    final uploadState = agentCubit.state;

    if (uploadState is AgentFileUploaded) {
      final snackBar = SnackBar(
        content: Text(
          isSignUp
              ? agentRegisteredSuccessfully
              : AppStrings.agentCreatedSuccessfully,
        ),
        duration: Duration(seconds: 2),
        backgroundColor: Colors.green,
      );

      // Show snackbar and wait until it's closed
      await scaffoldMessenger.showSnackBar(snackBar).closed;
      if (!isSignUp) {
        _clearForm();
      }
    } else if (uploadState is AgentError) {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
            '${AppStrings.fileUploadFailed}: ${uploadState.message}',
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  _showClearDataAlert(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(AppStrings.clearData),
          content: Text(
            AppStrings.clearDataConfirmation,
            textAlign: TextAlign.center,
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text(AppStrings.cancel),
            ),
            TextButton(
              onPressed: () {
                _clearForm();
                Navigator.pop(context);
              },
              child: Text(AppStrings.ok),
            ),
          ],
        );
      },
    );
  }

  _clearForm() {
    firstNameController.clear();
    lastNameController.clear();
    phoneController.clear();
    emailController.clear();
    cityController.clear();
    stateController.clear();
    postalCodeController.clear();
    countryController.clear();
    agentLicenseIdController.clear();
    additionalInfoController.clear();
    agentLicenseFile.value = null;
    referralCodeController.clear();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_formKey.currentState != null) {
        _formKey.currentState!.reset();
      }
    });
  }
}
