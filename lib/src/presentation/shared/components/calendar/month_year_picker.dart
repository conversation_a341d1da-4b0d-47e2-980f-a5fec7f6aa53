import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:neorevv/src/core/theme/app_theme.dart';
import '../../../../core/config/responsive.dart';

class PickerPosition {
  final double left;
  final double top;

  const PickerPosition({required this.left, required this.top});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PickerPosition && other.left == left && other.top == top;
  }

  @override
  int get hashCode => left.hashCode ^ top.hashCode;
}

class MonthYearPicker extends StatefulWidget {
  final DateTime initialDate;
  final ValueChanged<DateTime> onDateSelected;
  final VoidCallback onCancel;
  final GlobalKey anchorKey;

  const MonthYearPicker({
    Key? key,
    required this.initialDate,
    required this.onDateSelected,
    required this.onCancel,
    required this.anchorKey,
  }) : super(key: key);

  @override
  State<MonthYearPicker> createState() => _MonthYearPickerState();
}

class _MonthYearPickerState extends State<MonthYearPicker> {
  late int selectedYear;
  late int selectedMonth;
  bool showingYears = false;
  int yearPageIndex = 0;

  final DateTime today = DateTime.now();
  final DateTime firstDate = DateTime(2015, 1, 1);
  late final DateTime lastDate = DateTime(today.year, 12, 31);

  @override
  void initState() {
    super.initState();
    selectedYear = widget.initialDate.year.clamp(firstDate.year, today.year);
    selectedMonth = widget.initialDate.month;
    if (selectedYear == today.year && selectedMonth > today.month) {
      selectedMonth = today.month;
    }
    final yearIndex = selectedYear - firstDate.year;
    yearPageIndex = yearIndex ~/ 12;
  }

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;
    final double pickerWidth = _calculatePickerWidth(screenWidth);
    return Theme(
      data: Theme.of(context).copyWith(
        colorScheme: ColorScheme.light(
          primary: AppTheme.primaryBlueColor,
          onPrimary: Colors.white,
          surface: Colors.white,
          onSurface: Colors.black,
        ),
      ),
      child: Material(
        elevation: 4.0,
        borderRadius: BorderRadius.circular(8.0),
        color: Colors.white,
        child: SizedBox(
          width: pickerWidth,
          height: 350,
          child: Column(
            children: [
              _buildHeader(),
              const SizedBox(height: 12),
              Expanded(
                child: showingYears ? _buildYearGrid() : _buildMonthGrid(),
              ),
              const SizedBox(height: 8),
              _buildActionButtons(),
              const SizedBox(height: 12),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8.0),
          topRight: Radius.circular(8.0),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: () {
              setState(() {
                if (showingYears) {
                  if (yearPageIndex > 0) {
                    yearPageIndex--;
                  }
                } else {
                  selectedYear = (selectedYear - 1)
                      .clamp(firstDate.year, lastDate.year)
                      .toInt();
                }
              });
            },
            icon: Icon(Icons.chevron_left, color: Colors.black),
          ),
          Material(
            child: InkWell(
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              mouseCursor: SystemMouseCursors.click,
              onTap: () {
                setState(() {
                  showingYears = !showingYears;
                  if (showingYears) {
                    final yearIndex = selectedYear - firstDate.year;
                    yearPageIndex = yearIndex ~/ 12;
                  }
                });
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: Text(
                  showingYears
                      ? _getYearPageRangeText()
                      : selectedYear.toString(),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                  ),
                ),
              ),
            ),
          ),
          Material(
            child: InkWell(
              mouseCursor: SystemMouseCursors.click,
              hoverColor: Colors.transparent,
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              child: IconButton(
                onPressed: () {
                  setState(() {
                    if (showingYears) {
                      final totalYears = lastDate.year - firstDate.year + 1;
                      final maxPageIndex = (totalYears - 1) ~/ 12;
                      if (yearPageIndex < maxPageIndex) {
                        yearPageIndex++;
                      }
                    } else {
                      selectedYear = (selectedYear + 1)
                          .clamp(firstDate.year, lastDate.year)
                          .toInt();
                    }
                  });
                },
                icon: Icon(Icons.chevron_right, color: Colors.black),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getYearPageRangeText() {
    final startYear = firstDate.year + (yearPageIndex * 12);
    final totalYears = lastDate.year - firstDate.year + 1;
    final remainingYears = totalYears - (yearPageIndex * 12);
    final endYear = startYear + (remainingYears > 12 ? 11 : remainingYears - 1);
    return '$startYear - $endYear';
  }

  Widget _buildYearGrid() {
    final startYear = firstDate.year + (yearPageIndex * 12);
    final totalYears = lastDate.year - firstDate.year + 1;
    final remainingYears = totalYears - (yearPageIndex * 12);
    final yearsToShow = remainingYears > 12 ? 12 : remainingYears;

    List<int> years = [];
    for (int i = 0; i < yearsToShow; i++) {
      years.add(startYear + i);
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 2.8,
          crossAxisSpacing: 4,
          mainAxisSpacing: 4,
        ),
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: years.length,
        itemBuilder: (context, index) {
          final year = years[index];
          final isSelected = year == selectedYear;

          return Material(
            child: InkWell(
              mouseCursor: SystemMouseCursors.click,
              onTap: () {
                setState(() {
                  selectedYear = year;
                  // Reset selected month if selected year is now the current year and month > today.month
                  if (selectedYear == today.year &&
                      selectedMonth > today.month) {
                    selectedMonth = today.month;
                  }
                  showingYears = false;
                });
              },
              child: Container(
                decoration: BoxDecoration(
                  color: isSelected ? AppTheme.primaryBlueColor : Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected
                        ? AppTheme.primaryBlueColor
                        : Colors.grey.shade300,
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Text(
                    year.toString(),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isSelected ? Colors.white : Colors.black,
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMonthGrid() {
    final maxMonth = selectedYear < today.year ? 12 : today.month;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: GridView.builder(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 2.5,
          crossAxisSpacing: 6,
          mainAxisSpacing: 6,
        ),
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemCount: maxMonth,
        itemBuilder: (context, index) {
          final month = index + 1;
          final isSelected = month == selectedMonth;

          return Material(
            child: InkWell(
              mouseCursor: SystemMouseCursors.click,
              onTap: () {
                setState(() {
                  selectedMonth = month;
                });
              },
              child: Container(
                decoration: BoxDecoration(
                  color: isSelected ? AppTheme.primaryBlueColor : Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: isSelected
                        ? AppTheme.primaryBlueColor
                        : Colors.grey.shade300,
                    width: 1,
                  ),
                ),
                child: Center(
                  child: Text(
                    DateFormat('MMM').format(DateTime(selectedYear, month)),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: isSelected ? Colors.white : Colors.black,
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          TextButton(
            onPressed: widget.onCancel,
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              backgroundColor: Colors.grey.shade200,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Cancel',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
          ),
          const SizedBox(width: 12),
          TextButton(
            onPressed: () {
              widget.onDateSelected(DateTime(selectedYear, selectedMonth, 1));
            },
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
              backgroundColor: AppTheme.primaryBlueColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'SET',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

double _calculatePickerWidth(double screenWidth) {
  if (screenWidth <= 480) {
    return (screenWidth - 40).clamp(280, screenWidth - 20);
  } else if (screenWidth <= 800) {
    return (screenWidth * 0.8).clamp(300, 400);
  } else {
    return 320;
  }
}
