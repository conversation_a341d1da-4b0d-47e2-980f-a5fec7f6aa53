import 'package:bloc/bloc.dart';
import 'package:flutter/rendering.dart';
import 'package:meta/meta.dart';
import '../../../core/services/exceptions.dart';
import '../../../domain/models/RegisteredAgent.dart';
import '../../../domain/models/agent_model.dart';
import '../../../domain/repository/agent_repository.dart';

part 'agent_state.dart';

class AgentCubit extends Cubit<AgentState> {
  final AgentRepository _agentRepository;

  AgentCubit(this._agentRepository) : super(AgentInitial());

  /// Get list of agents with POST request
  Future<void> getAgents({
    required Map<String, dynamic> requestBody,
    bool loadMore = false,
  }) async {
    if (!loadMore) {
      emit(AgentLoading());
    }

    try {
      final response = await _agentRepository.getAgents(requestBody);

      emit(
        AgentLoaded(
          agents: response.content,
          totalCount: response.totalElements,
          currentPage: response.number,
          hasMore: !response.last,
          totalPages: response.totalPages,
        ),
      );
    } on ApiException catch (e) {
      emit(AgentError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        AgentError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  /// Get agent details by ID
  Future<void> getAgentById(String agentId) async {
    emit(AgentLoading());

    try {
      final agent = await _agentRepository.getAgentById(agentId);
      emit(
        AgentLoaded(
          agents: [agent],
          totalCount: 1,
          currentPage: 0,
          hasMore: false,
          totalPages: 1,
        ),
      );
    } on ApiException catch (e) {
      emit(AgentError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        AgentError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  /// Clear current state
  void clearAgents() {
    emit(AgentInitial());
  }

  /// Create a new agent
  Future<void> resgiterAgent(Map<String, dynamic> requestBody) async {
    emit(AgentLoading());
    try {
      final response = await _agentRepository.registerAgent(requestBody);
      if (response != false) {
        // Extract userid from response data
        String? userId;

        final data = response['data'];
        userId = data['userID'];
        emit(
          AgentCreated(
            userId: userId,
            responseData: response is Map<String, dynamic> ? response : null,
          ),
        );
      } else {
        emit(AgentError(message: 'Failed to create agent'));
      }
    } on ApiException catch (e) {
      emit(AgentError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        AgentError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  /// Upload agent file
  Future<void> uploadAgentFile(Map<String, dynamic> requestBody) async {
    emit(AgentLoading());
    try {
      final success = await _agentRepository.uploadAgentFile(requestBody);
      if (success) {
        emit(AgentFileUploaded());
      } else {
        emit(AgentError(message: 'Failed to upload agent file'));
      }
    } on ApiException catch (e) {
      emit(AgentError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        AgentError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  /// Get registered agent info
  Future<void> getRegisteredAgentInfo(String id) async {
    try {
      final agent = await _agentRepository.fetchRegisteredAgentInfo(id);
      emit(AgentRegisteredInfoLoaded(registeredAgent: agent));
    } on ApiException catch (e) {
      emit(AgentError(message: e.message, statusCode: e.statusCode));
    } catch (e) {
      emit(
        AgentError(message: 'An unexpected error occurred: ${e.toString()}'),
      );
    }
  }

  /// Signup agent
  Future<void> signupAgent(Map<String, dynamic> requestBody) async {
    emit(AgentLoading());
    try {
      final response = await _agentRepository.signupAgent(requestBody);
      if (response != false) {
        // Extract userid from response data
        String? userId;

        final data = response['data'];
        userId = data['userID'];
        emit(AgentSignupSuccess(userId: userId));
      } else {
        emit(AgentSignupError(error: 'Failed to create agent'));
      }
    } on ApiException catch (e) {
      emit(AgentSignupError(error: e.message));
    } catch (e) {
      emit(
        AgentSignupError(
          error: 'An unexpected error occurred: ${e.toString()}',
        ),
      );
    }
  }
}
