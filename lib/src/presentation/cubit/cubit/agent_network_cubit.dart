import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import 'package:neorevv/src/core/config/json_consts.dart';
import 'package:neorevv/src/domain/models/agent_network_model.dart';
import 'package:flutter/material.dart';
import 'package:neorevv/src/domain/models/agent.dart';
import 'package:neorevv/src/domain/models/broker.dart';
import 'package:neorevv/src/domain/repository/get_network_item_repository.dart';

import '../../../core/network/api_config.dart';

part 'agent_network_state.dart';

class AgentNetworkCubit extends Cubit<AgentNetworkState> {
  AgentNetworkCubit(this._agentNetworkRepository)
    : super(AgentNetworkInitial());

  final GetNetworkItemRepository _agentNetworkRepository;

  List<Agent> _hierarchyPath = [];
  Broker? _currentBroker;
  String? _initialUserId;

  Future<void> fetchNetworkItems(String userId, {bool isRoot = false}) async {
    emit(AgentNetworkLoading());
    _initialUserId ??= userId;
    try {
      final response = await _agentNetworkRepository.getNetworkItem(
        APIConfig.agentNetwork,
        userId,
      );
      final agent = mapAgentNetworkModelToAgent(response);
      final broker = mapAgentNetworkModelToBroker(response);
      if (isRoot || _currentBroker == null) {
        _currentBroker = broker;
        _hierarchyPath.clear();
      }
      final upstreamresponse = await _agentNetworkRepository.getUpstreamUsers(
        APIConfig.upstreamUsers,
        _initialUserId!,
      );
      List<Agent> upstreamUsers = upstreamresponse
          .map(
            (e) => Agent(
              id: e.userId,
              name: e.name,
              sales: 0,
              amount: 0.0,
              commission: 0.0,
              contact: '',
              email: e.email,
              agents: [],
              totalAgents: e.recruitsCount,
              color: Colors.blue,
              imageUrl: '',
              joinDate: DateTime.now(),
              state: '',
              city: '',
              level: '',
              totalDeals: 0,
              earning: 0.0,
              status: true,
              relatedBroker: '',
              soldHomes: 0,
              totalSales: 0,
              role: e.role,
            ),
          )
          .toList();

      emit(
        AgentNetworkSuccess(
          agent: agent,
          broker: _currentBroker ?? broker,
          hierarchyPath: List.from(_hierarchyPath),
          upstreamUsers: upstreamUsers,
        ),
      );
    } catch (e) {
      emit(AgentNetworkError(message: e.toString()));
    }
  }

  void navigateToAgent(Agent agent) {
    // Add agent to hierarchy path
    final existingIndex = _hierarchyPath.indexWhere((a) => a.id == agent.id);

    if (existingIndex != -1) {
      // If agent exists in path, truncate to that point
      _hierarchyPath = _hierarchyPath.sublist(0, existingIndex + 1);
    } else {
      // Add new agent to path
      _hierarchyPath.add(agent);
    }

    // Fetch network data for this agent
    fetchNetworkItems(agent.id);
  }

  void navigateToLevel(int level) {
    if (level < _hierarchyPath.length) {
      final newPath = _hierarchyPath.sublist(0, level + 1);
      _hierarchyPath = newPath;

      // Fetch network data for the agent at this level
      final targetAgent = _hierarchyPath.last;
      fetchNetworkItems(targetAgent.id);
    } else if (level == 0 && _hierarchyPath.isNotEmpty) {
      // Navigate back to root (broker level)
      _hierarchyPath.clear();
      if (_currentBroker != null) {
        fetchNetworkItems(_currentBroker!.id, isRoot: true);
      }
    }
  }

  void resetHierarchy() {
    _hierarchyPath.clear();
    _currentBroker = null;
    _initialUserId = null;
  }
}

Agent mapAgentNetworkModelToAgent(AgentNetworkModel model) {
  return Agent(
    id: model.userProfile.userId,
    name: model.userProfile.name,
    sales: 0,
    amount: model.userProfile.totalSalesRevenue.toDouble(),
    commission: model.userProfile.totalCommissionEarned.toDouble(),
    contact: model.userProfile.phone,
    email: model.userProfile.email,
    agents: model.directRecruits.map(mapRecruitToAgent).toList(),
    totalAgents: model.totalDirectRecruits,
    color: Colors.blue,
    imageUrl: model.userProfile.profileImageUrl ?? '',
    joinDate: DateTime.now(),
    state: '',
    city: '',
    level: model.userProfile.role,
    totalDeals: 0,
    earning: model.userProfile.totalCommissionEarned.toDouble(),
    status: true,
    relatedBroker: '',
    soldHomes: 0,
    totalSales: 0,
    role: model.userProfile.role,
  );
}

Agent mapRecruitToAgent(DirectRecruit recruit) {
  return Agent(
    id: recruit.userId,
    name: recruit.name,
    sales: 0,
    amount: 0.0,
    commission: 0.0,
    contact: '',
    email: recruit.email,
    agents: [],
    totalAgents: recruit.recruitsCount,
    color: Colors.green,
    imageUrl: '',
    joinDate: DateTime.now(),
    state: '',
    city: '',
    level: recruit.role,
    totalDeals: 0,
    earning: 0.0,
    status: true,
    relatedBroker: '',
    soldHomes: 0,
    totalSales: 0,
    role: recruit.role,
  );
}

Broker mapAgentNetworkModelToBroker(AgentNetworkModel model) {
  return Broker(
    id: model.userProfile.userId,
    name: model.userProfile.name,
    sales: model.totalDirectRecruits,
    imageUrl: model.userProfile.profileImageUrl ?? '',
    contact: model.userProfile.phone,
    email: model.userProfile.email,
    totalAgents: model.userProfile.recruitsCount,
    agents: model.directRecruits.map(mapRecruitToAgent).toList(),
    totalSalesRevenue: model.userProfile.totalSalesRevenue.toDouble(),
    totalCommission: model.userProfile.totalCommissionEarned.toDouble(),
    color: Colors.blue,
    joinDate: DateTime.now(),
    role: model.userProfile.role,
  );
}
