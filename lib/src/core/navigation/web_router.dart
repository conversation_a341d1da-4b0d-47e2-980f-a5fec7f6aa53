import 'package:go_router/go_router.dart';
import '/src/presentation/screens/auth/create_password.dart';
import '/src/presentation/screens/layout/signup_layout.dart';
import '../../presentation/screens/agent/agent_registration_screen.dart';
import '../../presentation/screens/broker/register_broker_screen.dart';
import '/main_layout_screen.dart';
import '../../presentation/screens/auth/auth_wrapper.dart';

enum AppRoutes {
  login('/'),
  mainLayout('/home'),
  dashboard('/home/<USER>'),
  brokerages('/home/<USER>'),
  agents('/home/<USER>'),
  sales('/home/<USER>'),
  reports('/home/<USER>'),
  registerBroker('/home/<USER>'),
  registerAgent('/home/<USER>'),
  agentNetwork('/home/<USER>'),
  saleReviewDoc('/home/<USER>'),

  signupBroker('/signup-brokerage'),
  signupAgent('/signup-agent'),
  createPassword('/create-password');

  const AppRoutes(this.path);
  final String path;
}

final GoRouter appRouter = GoRouter(
  initialLocation: AppRoutes.login.path,
  routes: [
    GoRoute(
      path: AppRoutes.login.path,
      builder: (context, state) => const AuthWrapper(),
    ),
    GoRoute(
      path: '/home/<USER>',
      builder: (context, state) {
        final tab = state.pathParameters['tab'] ?? 'dashboard';
        return MainLayoutScreen(initialTab: tab);
      },
    ),
    GoRoute(
      path: AppRoutes.mainLayout.path,
      redirect: (context, state) => AppRoutes.dashboard.path,
    ),
    GoRoute(
      path: AppRoutes.signupBroker.path,
      builder: (context, state) {
        final token = state.uri.queryParameters['invite-id'];
        return SignupLayoutScreen(child: RegisterBrokerScreen());
      },
    ),
    GoRoute(
      path: AppRoutes.signupAgent.path,
      builder: (context, state) {
        final token = state.uri.queryParameters['invite-id'] ?? "";
        return SignupLayoutScreen(
          child: AgentRegistrationScreen(inviteId: token, isSignUp: true),
        );
      },
    ),
    GoRoute(
      path: AppRoutes.createPassword.path,
      builder: (context, state) {
        final token = state.uri.queryParameters['invite-id'];
        String? userId = state.uri.queryParameters['user-id'];
        String? email = state.uri.queryParameters['email'];

        final extra = state.extra as Map<String, dynamic>?;

        // when pass values from signup
        email ??= extra?['email'] as String?;
        userId ??= extra?['userId'] as String?;

        return SignupLayoutScreen(
          child: CreatePasswordScreen(
            inviteId: token,
            email: email,
            userId: userId,
          ),
        );
      },
    ),
  ],
);
