import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

class FirebaseAuthService {
  static final FirebaseAuthService _instance = FirebaseAuthService._internal();
  factory FirebaseAuthService() => _instance;
  FirebaseAuthService._internal();

  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;

  /// Sign in with Google using Firebase Auth directly
  Future<GoogleSignInResult> signInWithGoogle() async {
    try {
      // Create a new provider
      GoogleAuthProvider googleProvider = GoogleAuthProvider();

      googleProvider.addScope(
        'https://www.googleapis.com/auth/contacts.readonly',
      );
      googleProvider.addScope('email');
      googleProvider.addScope('profile');

      // Sign in with popup for web
      final UserCredential userCredential = await _firebaseAuth.signInWithPopup(
        googleProvider,
      );

      // Get the ID token for backend verification
      final String? idToken = await userCredential.user?.getIdToken();

      return GoogleSignInResult(
        success: true,
        user: userCredential.user,
        idToken: idToken,
        accessToken: null, // Not available with this method
        email: userCredential.user?.email,
        displayName: userCredential.user?.displayName,
        photoUrl: userCredential.user?.photoURL,
      );
    } catch (e) {
      debugPrint('Error signing in with Google: $e');
      return GoogleSignInResult(success: false, error: e.toString());
    }
  }

  /// Sign in with Apple using Firebase Auth

  Future<GoogleSignInResult> signInWithApple() async {
    try {
      // Create Apple provider for Firebase Auth
      final appleProvider = OAuthProvider("apple.com");
      appleProvider.addScope('email');
      appleProvider.addScope('name');

      // Set custom parameters for Apple Sign-In
      appleProvider.setCustomParameters({'locale': 'en'});
      // Sign in with popup for web
      final UserCredential userCredential = await _firebaseAuth.signInWithPopup(
        appleProvider,
      );
      // Get the ID token for backend verification
      final String? idToken = await userCredential.user?.getIdToken();
      return GoogleSignInResult(
        success: true,
        user: userCredential.user,
        idToken: idToken,
        accessToken: null, // Not available with this method
        email: userCredential.user?.email,
        displayName: userCredential.user?.displayName,
        photoUrl: userCredential.user?.photoURL,
      );
    } catch (e, stackTrace) {
      return GoogleSignInResult(success: false, error: e.toString());
    }
  }

  /// Sign out from Firebase
  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
    } catch (e) {
      debugPrint('Error signing out: $e');
      rethrow;
    }
  }

  /// Get current Firebase user
  User? get currentUser => _firebaseAuth.currentUser;

  /// Check if user is signed in
  bool get isSignedIn => _firebaseAuth.currentUser != null;

  /// Get current user's ID token
  Future<String?> getCurrentUserIdToken() async {
    try {
      return await _firebaseAuth.currentUser?.getIdToken();
    } catch (e) {
      debugPrint('Error getting ID token: $e');
      return null;
    }
  }

  /// Listen to auth state changes
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();
}

/// Result class for Google Sign-In operations
class GoogleSignInResult {
  final bool success;
  final String? error;
  final User? user;
  final String? idToken;
  final String? accessToken;
  final String? email;
  final String? displayName;
  final String? photoUrl;

  GoogleSignInResult({
    required this.success,
    this.error,
    this.user,
    this.idToken,
    this.accessToken,
    this.email,
    this.displayName,
    this.photoUrl,
  });

  @override
  String toString() {
    return 'GoogleSignInResult(success: $success, error: $error, email: $email)';
  }
}
