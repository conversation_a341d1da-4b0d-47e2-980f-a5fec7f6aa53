import 'package:flutter/material.dart';

import '../../domain/models/agent.dart';
import '../../domain/models/broker.dart';
import '../../domain/models/info_card.dart';
import '../../domain/models/sales_data.dart';
import '../theme/app_theme.dart';
import 'constants.dart';

final List<InfoCardData> infoCardsOwnerAdmin = [
  InfoCardData(
    title: "Total Brokers",
    value: "168",
    assetImage: '$iconAssetpath/brokers.png',
    iconColor: Colors.blue,
    subtitle: "Last Month",
    additionalInfo: "38",
  ),
  InfoCardData(
    title: "Total Agents",
    value: "2850",
    assetImage: '$iconAssetpath/agents.png',
    iconColor: Colors.blue,
    subtitle: "Last Month",
    additionalInfo: "125",
  ),
  InfoCardData(
    title: "Total Sales",
    value: "20K",
    assetImage: '$iconAssetpath/sales.png',
    iconColor: Colors.blue,
    subtitle: "Year",
    additionalInfo: "2025",
  ),
  InfoCardData(
    title: "Total Revenue",
    value: "\$250K",
    assetImage: '$iconAssetpath/revenue.png',
    iconColor: Colors.blue,
    subtitle: "Year",
    additionalInfo: "2025",
  ),
];

final List<InfoCardData> infoCardsBrokerage = [
  InfoCardData(
    title: "Sold Homes",
    value: "168",
    assetImage: '$iconAssetpath/sold_home.png',
    iconColor: Colors.blue,
    subtitle: "Last Month",
    additionalInfo: "38",
  ),
  InfoCardData(
    title: "Total Agents",
    value: "2850",
    assetImage: '$iconAssetpath/agents.png',
    iconColor: Colors.blue,
    subtitle: "Last Month",
    additionalInfo: "125",
  ),
  InfoCardData(
    title: "Total Value of Homes",
    value: "20K",
    assetImage: '$iconAssetpath/revenue.png',
    iconColor: Colors.blue,
    subtitle: "Year",
    additionalInfo: "2025",
  ),
  InfoCardData(
    title: "Total Commissions",
    value: "\$250K",
    assetImage: '$iconAssetpath/revenue.png',
    iconColor: Colors.blue,
    subtitle: "Year",
    additionalInfo: "2025",
  ),
];

final List<InfoCardData> infoCardsAgents = [
  InfoCardData(
    title: "Total Agents",
    value: "2850",
    assetImage: '$iconAssetpath/agents.png',
    iconColor: Colors.blue,
    subtitle: "Last Month",
    additionalInfo: "125",
  ),
  InfoCardData(
    title: "Sold Homes",
    value: "168",
    assetImage: '$iconAssetpath/sold_home.png',
    iconColor: Colors.blue,
    subtitle: "Last Month",
    additionalInfo: "38",
  ),

  InfoCardData(
    title: "Personal Commissions",
    value: "20K",
    assetImage: '$iconAssetpath/revenue.png',
    iconColor: Colors.blue,
    subtitle: "Year",
    additionalInfo: "2025",
  ),
  InfoCardData(
    title: "Total Commissions",
    value: "\$250K",
    assetImage: '$iconAssetpath/revenue.png',
    iconColor: Colors.blue,
    subtitle: "Year",
    additionalInfo: "2025",
  ),
];

// sample data with hierarchical structure
final agentListJson = [
  Agent(
    id: "1",
    name: "Sophia Turner(A1)",
    sales: 22,
    amount: 3400,
    commission: 510,
    contact: "(225) 555-0101",
    email: "<EMAIL>",
    relatedBroker: "Charlotte Anderson B1",
    agents: [
      Agent(
        id: "2",
        name: "Agent A1.1",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        id: "3",
        name: "Agent A1.2",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        id: "4",
        name: "Agent A1.3",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
    ],
    color: AppTheme.salesColors[9],
    imageUrl: "$imageAssetpath/profile.png",
    joinDate: DateTime(2025, 6, 28),
    state: "California",
    city: "Berkeley",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    id: "5",
    name: "Liam Johnson(B1)",
    sales: 18,
    amount: 2900,
    commission: 430,
    contact: "(225) 555-0102",
    email: "<EMAIL>",
    relatedBroker: "Charlotte Anderson B1",
    agents: [
      Agent(
        id: "6",
        name: "Agent B1.1",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        id: "7",
        name: "Agent B1.2",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        id: "8",
        name: "Agent B1.3",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
    ],
    color: AppTheme.primaryBlueColor,
    imageUrl: "$imageAssetpath/profile.png",
    joinDate: DateTime(2025, 7, 3),
    state: "Colorado",
    city: "Walsenburg",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    id: "9",
    name: "Olivia Smith(C1)",
    sales: 25,
    amount: 4100,
    commission: 620,
    contact: "(225) 555-0103",
    email: "<EMAIL>",
    relatedBroker: "Charlotte Anderson B1",
    agents: [
      Agent(
        id: "10",
        name: "Agent C1.1",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        id: "11",
        name: "Agent C1.2",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        id: "12",
        name: "Agent C1.3",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
    ],
    color: AppTheme.salesColors[8],

    imageUrl: "$imageAssetpath/profile.png",
    joinDate: DateTime(2025, 5, 20),
    state: "Georgia",
    city: "La Grange",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    id: "13",
    name: "Clint Barton(D1)",
    sales: 22,
    amount: 3400,
    commission: 510,
    contact: "(225) 555-0101",
    email: "<EMAIL>",
    relatedBroker: "Charlotte Anderson B1",
    agents: [
      Agent(
        id: "14",
        name: "Agent D1.1",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.salesColors[1],
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        id: "15",
        name: "Agent D1.2",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.salesColors[1],
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        id: "16",
        name: "Agent D1.3",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.primaryColor,
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
    ],
    color: AppTheme.salesColors[3],

    imageUrl: "$imageAssetpath/profile.png",
    joinDate: DateTime(2025, 6, 10),
    state: "Idaho",
    city: "Caldwell",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    id: "17",
    name: "Tony Stark(E1)",
    sales: 22,
    amount: 3400,
    commission: 510,
    contact: "(225) 555-0101",
    email: "<EMAIL>",
    relatedBroker: "Charlotte Anderson B1",
    agents: [
      Agent(
        id: "18",
        name: "Agent E1.1",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.salesColors[5],
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        id: "19",
        name: "Agent E1.2",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.salesColors[10],
        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
      Agent(
        id: "20",
        name: "Agent E1.3",
        sales: 22,
        amount: 3400,
        commission: 510,
        contact: "(225) 555-0101",
        email: "<EMAIL>",
        agents: [],
        color: AppTheme.salesColors[2],

        imageUrl: "$imageAssetpath/profile.png",
        joinDate: DateTime.now(),
        state: "California",
        city: "Berkeley",
        level: "Level 1",
        totalDeals: 12,
        earning: 2200,
        status: true,
      ),
    ],
    color: AppTheme.salesColors[7],

    imageUrl: "$imageAssetpath/profile.png",
    joinDate: DateTime(2025, 7, 1),
    state: "Indiana",
    city: "New Castle",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: false,
  ),
  Agent(
    id: "21",
    name: "Jake Williams(F1)",
    sales: 20,
    amount: 3600,
    commission: 540,
    contact: "(225) 555-0104",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[2],

    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "Charlotte Anderson B1",
    joinDate: DateTime(2025, 4, 18),
    state: "Iowa",
    city: "Des Moines",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    id: "22",
    name: "Emma Watson(G1)",
    sales: 20,
    amount: 3600,
    commission: 540,
    contact: "(225) 555-0104",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[4],

    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "Charlotte Anderson B1",
    joinDate: DateTime(2025, 6, 30),
    state: "Kansas",
    city: "Wichita",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    id: "23",
    name: "Noah Williams(A2)",
    sales: 20,
    amount: 3600,
    commission: 540,
    contact: "(225) 555-0104",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.primaryColor,
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: 'Benjamin Thomas B2',
    joinDate: DateTime(2025, 5, 5),
    state: "Kentucky",
    city: "Louisville",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    id: "24",
    name: "Emma Brown(A2)",
    sales: 16,
    amount: 2700,
    commission: 400,
    contact: "(225) 555-0105",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.primaryBlueColor,
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: 'Benjamin Thomas B2',
    joinDate: DateTime(2025, 5, 12),
    state: "Louisiana",
    city: "Baton Rouge",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: false,
  ),
  Agent(
    id: "25",
    name: "Mason Davis(A2)",
    sales: 19,
    amount: 3200,
    commission: 480,
    contact: "(225) 555-0106",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.commissionCardDarkColor,
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: 'Benjamin Thomas B2',
    joinDate: DateTime(2025, 7, 6),
    state: "Maine",
    city: "Augusta",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    id: "26",
    name: "Ava Miller(A3)",
    sales: 23,
    amount: 3900,
    commission: 590,
    contact: "(225) 555-0107",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[11],

    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: 'Mia Jackson B3',
    joinDate: DateTime(2025, 3, 15),
    state: "Maryland",
    city: "Baltimore",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    id: "27",
    name: "Elijah Wilson(A3)",
    sales: 21,
    amount: 3500,
    commission: 530,
    contact: "(225) 555-0108",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[10],

    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: 'Mia Jackson B3',
    joinDate: DateTime(2025, 4, 10),
    state: "Massachusetts",
    city: "Boston",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    id: "28",
    name: "Isabella Moore(A3)",
    sales: 17,
    amount: 2800,
    commission: 420,
    contact: "(225) 555-0109",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[9],

    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: 'Mia Jackson B3',
    joinDate: DateTime(2025, 5, 25),
    state: "Michigan",
    city: "Lansing",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    id: "29",
    name: "James Taylor(A4)",
    sales: 15,
    amount: 2500,
    commission: 380,
    contact: "(225) 555-0110",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[8],
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "William White B4",
    joinDate: DateTime(2025, 6, 28),
    state: "California",
    city: "Berkeley",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    id: "30",
    name: "Mia White(A4)",
    sales: 14,
    amount: 2300,
    commission: 350,
    contact: "(225) 555-0111",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[7],

    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "William White B4",
    joinDate: DateTime(2025, 7, 3),
    state: "Colorado",
    city: "Walsenburg",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    id: "31",
    name: "Jackson Lee(A4)",
    sales: 13,
    amount: 2100,
    commission: 320,
    contact: "(225) 555-0112",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[6],
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "William White B4",
    joinDate: DateTime(2025, 5, 20),
    state: "Georgia",
    city: "La Grange",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    id: "32",
    name: "Amelia Robinson(A5)",
    sales: 12,
    amount: 2000,
    commission: 300,
    contact: "(225) 555-0113",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[5],
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "Amelia Harris B5",
    joinDate: DateTime(2025, 6, 10),
    state: "Idaho",
    city: "Caldwell",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    id: "33",
    name: "Chris Hemworth(A5)",
    sales: 12,
    amount: 2000,
    commission: 300,
    contact: "(225) 555-0113",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[3],
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "Amelia Harris B5",
    joinDate: DateTime(2025, 7, 1),
    state: "Indiana",
    city: "New Castle",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: false,
  ),
  Agent(
    id: "34",
    name: "Bruce Banner(A5)",
    sales: 12,
    amount: 2000,
    commission: 300,
    contact: "(225) 555-0113",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[4],
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "Amelia Harris B5",
    joinDate: DateTime(2025, 4, 18),
    state: "Iowa",
    city: "Des Moines",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    id: "35",
    name: "Bruce Banner(A5)",
    sales: 12,
    amount: 2000,
    commission: 300,
    contact: "(225) 555-0113",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[0],
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "Amelia Harris B5",
    joinDate: DateTime(2025, 6, 30),
    state: "Kansas",
    city: "Wichita",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
  Agent(
    id: "36",
    name: "Natasha Romanoff(A6)",
    sales: 12,
    amount: 2000,
    commission: 300,
    contact: "(225) 555-0113",
    email: "<EMAIL>",
    agents: [],
    color: AppTheme.salesColors[1],
    imageUrl: "$imageAssetpath/profile.png",
    relatedBroker: "Lucas Martin B6",
    joinDate: DateTime(2025, 5, 5),
    state: "Kentucky",
    city: "Louisville",
    level: "Level 1",
    totalDeals: 12,
    earning: 2200,
    status: true,
  ),
];

///
///
final brokersListJson = [
  Broker(
    id: '1',
    name: "Charlotte Anderson B1",
    sales: 12,
    contact: "(415) 555-0101",
    email: "<EMAIL>",

    agents: [...agentListJson.sublist(0, 8)],
    totalAgents: 8,
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 15000,
    totalCommission: 8000,
    color: AppTheme.salesColors[0],
    joinDate: DateTime(2022, 3, 15),
  ),
  Broker(
    id: '2',
    name: "Benjamin Thomas B2",
    sales: 30,
    contact: "(415) 555-0102",
    email: "<EMAIL>",
    agents: agentListJson.sublist(8, 11),
    totalAgents: 3,
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 32000,
    totalCommission: 12000,
    color: AppTheme.salesColors[1],
    joinDate: DateTime(2021, 8, 22),
  ),
  Broker(
    id: '3',
    name: "Mia Jackson B3",
    sales: 8,
    contact: "(415) 555-0103",
    email: "<EMAIL>",
    agents: agentListJson.sublist(2, 8),
    totalAgents: 6,
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 9000,
    totalCommission: 3500,
    color: AppTheme.salesColors[2],
    joinDate: DateTime(2023, 1, 10),
  ),
  Broker(
    id: '4',
    name: "William White B4",
    sales: 25,
    contact: "(415) 555-0104",
    email: "<EMAIL>",
    agents: agentListJson.sublist(3, 10),
    totalAgents: 7,
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 25000,
    totalCommission: 10000,
    color: AppTheme.salesColors[3],
    joinDate: DateTime(2022, 11, 5),
  ),
  Broker(
    id: '5',
    name: "Amelia Harris B5",
    sales: 13,
    contact: "(415) 555-0105",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 6),
    totalAgents: 6,
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 14000,
    totalCommission: 6000,
    color: AppTheme.salesColors[4],
    joinDate: DateTime(2023, 6, 18),
  ),
  Broker(
    id: '6',
    name: "Lucas Martin B6",
    sales: 10,
    contact: "(415) 555-0106",
    email: "<EMAIL>",
    agents: agentListJson.sublist(3, 10),
    totalAgents: 5,
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 11000,
    totalCommission: 4000,
    color: AppTheme.salesColors[5],
    joinDate: DateTime(2022, 7, 30),
  ),
  Broker(
    id: '7',
    name: "Harper Lee B7",
    sales: 30,
    contact: "(415) 555-0107",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    totalAgents: 10,
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 33000,
    totalCommission: 15000,
    color: AppTheme.salesColors[6],
    joinDate: DateTime(2021, 12, 14),
  ),
  Broker(
    id: '8',
    name: "Henry Clark B8",
    sales: 58,
    contact: "(415) 555-0108",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    totalAgents: 10,
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 60000,
    totalCommission: 22000,
    color: AppTheme.salesColors[7],
    joinDate: DateTime(2020, 4, 8),
  ),
  Broker(
    id: '9',
    name: "Evelyn Lewis B9",
    sales: 42,
    contact: "(415) 555-0109",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    totalAgents: 10,
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 42000,
    totalCommission: 17000,
    color: AppTheme.salesColors[8],
    joinDate: DateTime(2021, 9, 25),
  ),
  Broker(
    id: '10',
    name: "Jack Walker B10",
    sales: 30,
    contact: "(415) 555-0110",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    totalAgents: 10,
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 31000,
    totalCommission: 12000,
    color: AppTheme.salesColors[9],
    joinDate: DateTime(2023, 2, 12),
  ),
  Broker(
    id: '11',
    name: "Ella Hall B11",
    sales: 35,
    contact: "(415) 555-0111",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    totalAgents: 10,
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 35000,
    totalCommission: 14000,
    color: AppTheme.salesColors[10],
    joinDate: DateTime(2022, 5, 20),
  ),
  Broker(
    id: '12',
    name: "Alexander Young B12",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    totalAgents: 10,
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 4400,
    totalCommission: 1800,
    color: AppTheme.salesColors[4],
    joinDate: DateTime(2021, 10, 3),
  ),
  Broker(
    id: '13',
    name: "Alexander Young B13",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    totalAgents: 10,
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 4400,
    totalCommission: 1800,
    color: AppTheme.salesColors[3],
    joinDate: DateTime(2021, 10, 3),
  ),
  Broker(
    id: '14',
    name: "Alexander Young B14",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    totalAgents: 10,
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 44000,
    totalCommission: 18000,
    color: AppTheme.salesColors[1],
    joinDate: DateTime(2021, 10, 3),
  ),
  Broker(
    id: '15',
    name: "Alexander Young B15",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    totalAgents: 10,
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 4000,
    totalCommission: 1800,
    color: AppTheme.salesColors[11],
    joinDate: DateTime(2021, 10, 3),
  ),
  Broker(
    id: '16',
    name: "Alexander Young B16",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    totalAgents: 10,
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 5000,
    totalCommission: 1800,
    color: AppTheme.salesColors[5],
    joinDate: DateTime(2021, 10, 3),
  ),
  Broker(
    id: '17',
    name: "Alexander Young B17",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    agents: agentListJson.sublist(0, 10),
    totalAgents: 10,
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 4000,
    totalCommission: 1800,
    color: AppTheme.salesColors[11],
    joinDate: DateTime(2021, 10, 3),
  ),
  Broker(
    id: '18',
    name: "Alexander Young B18",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    totalAgents: 10,
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 4000,
    totalCommission: 1000,
    color: AppTheme.salesColors[6],
    joinDate: DateTime(2021, 10, 3),
  ),
  Broker(
    id: '19',
    name: "Broker19",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    totalAgents: 10,
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 2000,
    totalCommission: 1800,
    color: AppTheme.salesColors[1],
    joinDate: DateTime(2021, 10, 3),
  ),
  Broker(
    id: '20',
    name: "Broker20",
    sales: 42,
    contact: "(415) 555-0112",
    email: "<EMAIL>",
    totalAgents: 10,
    agents: agentListJson.sublist(0, 10),
    imageUrl: "$iconAssetpath/agent_round.png",
    totalSalesRevenue: 10000,
    totalCommission: 1800,
    color: AppTheme.salesColors[0],
    joinDate: DateTime(2021, 10, 3),
  ),
];
// Quick selection options
const List<String> quickSelectionOptions = [
  'Today',
  'Yesterday',
  'This Week',
  'Last Week',
  'This Month',
  'Last Month',
  //'This Quarter',
  //'Last Quarter',
  'This Year',
  //'Last Year',
  'Custom Range',
];
const List<String> weekdayHeaders = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
final List<SalesData> salesData = [
  SalesData(name: 'Jessica Miller', sales: 30, color: Color(0xFF8B1538)),
  SalesData(name: 'David Thompson', sales: 28, color: Color(0xFFFF9500)),
  SalesData(name: 'Ashley Robinson', sales: 25, color: Color(0xFF4A7C59)),
  SalesData(name: 'Michael Carter', sales: 20, color: Color(0xFF007AFF)),
  SalesData(name: 'Brittany Howard', sales: 18, color: Color(0xFF5AC8FA)),
  SalesData(name: 'James Parker', sales: 15, color: Color(0xFFFFCC02)),
  SalesData(name: 'Emily Johnson', sales: 14, color: Color(0xFF1D1D1F)),
  SalesData(name: 'Christopher Adams', sales: 12, color: Color(0xFFE91E63)),
  SalesData(name: 'Lauren Mitchell', sales: 8, color: Color(0xFF9013FE)),
  SalesData(name: 'Brian Sanders', sales: 5, color: Color(0xFF536DFE)),
];
// Sample Sales Data Response JSON with commission and commissionAmt fields
const String sampleSalesDataResponse = '''
{
  "status": "success",
  "salesData": [
    {
      "transactionId": "TXN100000",
      "agentName": "Alice Johnson",
      "propertyType": "Residential",
      "propertyAddress": "655 Main Street, Springfield, FL",
      "propertyValue": 758730,
      "buyerName": "Dwayne Johnson",
      "buyerAddress": "487 Oak Street, Fairview, IL",
      "listingDate": "2024-08-05T00:00:00Z",
      "saleDate": "2024-08-16T00:00:00Z",
      "salePrice": 830439.41,
      "commission": 3.5,
      "commissionAmt": 29065.38
    },
    {
      "transactionId": "TXN100001",
      "agentName": "Michael Lee",
      "propertyType": "Residential",
      "propertyAddress": "929 Main Street, Greenville, CA",
      "propertyValue": 605162,
      "buyerName": "Tom Hanks",
      "buyerAddress": "151 Pine Road, Centerville, IL",
      "listingDate": "2024-11-16T00:00:00Z",
      "saleDate": "2025-01-14T00:00:00Z",
      "salePrice": 596079.43,
      "commission": 2.0,
      "commissionAmt": 11921.59
    },
    {
      "transactionId": "TXN100002",
      "agentName": "Jane Smith",
      "propertyType": "Industrial",
      "propertyAddress": "887 Elm Street, Greenville, NY",
      "propertyValue": 565447,
      "buyerName": "Emma Watson",
      "buyerAddress": "350 Main Street, Fairview, CA",
      "listingDate": "2024-02-13T00:00:00Z",
      "saleDate": "2024-03-17T00:00:00Z",
      "salePrice": 611625.95,
      "commission": 3.5,
      "commissionAmt": 21406.91
    },
    {
      "transactionId": "TXN100003",
      "agentName": "Jane Smith",
      "propertyType": "Land",
      "propertyAddress": "390 Elm Street, Centerville, TX",
      "propertyValue": 314394,
      "buyerName": "Natalie Portman",
      "buyerAddress": "962 Elm Street, Riverside, NY",
      "listingDate": "2024-10-02T00:00:00Z",
      "saleDate": "2024-12-10T00:00:00Z",
      "salePrice": 298957.23,
      "commission": 2.5,
      "commissionAmt": 7473.93
    },
    {
      "transactionId": "TXN100004",
      "agentName": "Michael Lee",
      "propertyType": "Land",
      "propertyAddress": "317 Maple Avenue, Fairview, TX",
      "propertyValue": 222766,
      "buyerName": "Tom Hanks",
      "buyerAddress": "502 Pine Road, Centerville, TX",
      "listingDate": "2024-04-11T00:00:00Z",
      "saleDate": "2024-06-20T00:00:00Z",
      "salePrice": 214484.0,
      "commission": 2.0,
      "commissionAmt": 4289.68
    },
    {
      "transactionId": "TXN100005",
      "agentName": "Michael Lee",
      "propertyType": "Commercial",
      "propertyAddress": "369 Main Street, Riverside, CA",
      "propertyValue": 926515,
      "buyerName": "Natalie Portman",
      "buyerAddress": "851 Oak Street, Fairview, IL",
      "listingDate": "2024-01-29T00:00:00Z",
      "saleDate": "2024-02-20T00:00:00Z",
      "salePrice": 903513.55,
      "commission": 3.0,
      "commissionAmt": 27105.41
    },
    {
      "transactionId": "TXN100006",
      "agentName": "Jane Smith",
      "propertyType": "Residential",
      "propertyAddress": "758 Oak Street, Fairview, FL",
      "propertyValue": 968489,
      "buyerName": "Natalie Portman",
      "buyerAddress": "255 Oak Street, Fairview, TX",
      "listingDate": "2024-10-08T00:00:00Z",
      "saleDate": "2024-11-11T00:00:00Z",
      "salePrice": 990342.89,
      "commission": 3.5,
      "commissionAmt": 34662.0
    },
    {
      "transactionId": "TXN100007",
      "agentName": "Bob Brown",
      "propertyType": "Commercial",
      "propertyAddress": "218 Pine Road, Riverside, IL",
      "propertyValue": 292652,
      "buyerName": "Dwayne Johnson",
      "buyerAddress": "813 Maple Avenue, Centerville, FL",
      "listingDate": "2024-08-23T00:00:00Z",
      "saleDate": "2024-09-19T00:00:00Z",
      "salePrice": 318851.68,
      "commission": 3.5,
      "commissionAmt": 11159.81
    },
    {
      "transactionId": "TXN100008",
      "agentName": "John Doe",
      "propertyType": "Commercial",
      "propertyAddress": "689 Oak Street, Greenville, IL",
      "propertyValue": 298836,
      "buyerName": "Tom Hanks",
      "buyerAddress": "507 Oak Street, Greenville, IL",
      "listingDate": "2024-10-30T00:00:00Z",
      "saleDate": "2024-12-12T00:00:00Z",
      "salePrice": 303836.91,
      "commission": 2.5,
      "commissionAmt": 7595.92
    },
    {
      "transactionId": "TXN100009",
      "agentName": "Jane Smith",
      "propertyType": "Residential",
      "propertyAddress": "951 Maple Avenue, Springfield, IL",
      "propertyValue": 331913,
      "buyerName": "Chris Evans",
      "buyerAddress": "587 Elm Street, Fairview, IL",
      "listingDate": "2024-08-06T00:00:00Z",
      "saleDate": "2024-09-20T00:00:00Z",
      "salePrice": 325729.48,
      "commission": 2.0,
      "commissionAmt": 6514.59
    },
    {
      "transactionId": "TXN100010",
      "agentName": "Bob Brown",
      "propertyType": "Land",
      "propertyAddress": "531 Main Street, Riverside, FL",
      "propertyValue": 346848,
      "buyerName": "Dwayne Johnson",
      "buyerAddress": "779 Elm Street, Greenville, IL",
      "listingDate": "2024-11-18T00:00:00Z",
      "saleDate": "2025-02-13T00:00:00Z",
      "salePrice": 335372.23,
      "commission": 3.5,
      "commissionAmt": 11738.03
    },
    {
      "transactionId": "TXN100011",
      "agentName": "Alice Johnson",
      "propertyType": "Residential",
      "propertyAddress": "111 Main Street, Centerville, FL",
      "propertyValue": 572691,
      "buyerName": "Natalie Portman",
      "buyerAddress": "911 Elm Street, Greenville, CA",
      "listingDate": "2024-09-10T00:00:00Z",
      "saleDate": "2024-11-01T00:00:00Z",
      "salePrice": 623446.35,
      "commission": 3.0,
      "commissionAmt": 18703.39
    },
    {
      "transactionId": "TXN100012",
      "agentName": "Alice Johnson",
      "propertyType": "Residential",
      "propertyAddress": "309 Oak Street, Springfield, FL",
      "propertyValue": 817797,
      "buyerName": "Tom Hanks",
      "buyerAddress": "265 Pine Road, Fairview, CA",
      "listingDate": "2024-11-26T00:00:00Z",
      "saleDate": "2025-01-10T00:00:00Z",
      "salePrice": 848099.12,
      "commission": 3.0,
      "commissionAmt": 25442.97
    },
    {
      "transactionId": "TXN100013",
      "agentName": "Michael Lee",
      "propertyType": "Residential",
      "propertyAddress": "259 Elm Street, Riverside, FL",
      "propertyValue": 381020,
      "buyerName": "Emma Watson",
      "buyerAddress": "918 Maple Avenue, Fairview, TX",
      "listingDate": "2024-10-07T00:00:00Z",
      "saleDate": "2024-10-31T00:00:00Z",
      "salePrice": 407913.22,
      "commission": 3.0,
      "commissionAmt": 12237.4
    },
    {
      "transactionId": "TXN100014",
      "agentName": "Alice Johnson",
      "propertyType": "Residential",
      "propertyAddress": "426 Elm Street, Centerville, IL",
      "propertyValue": 804345,
      "buyerName": "Emma Watson",
      "buyerAddress": "862 Oak Street, Centerville, CA",
      "listingDate": "2024-06-17T00:00:00Z",
      "saleDate": "2024-09-10T00:00:00Z",
      "salePrice": 810766.7,
      "commission": 3.0,
      "commissionAmt": 24323.0
    },
    {
      "transactionId": "TXN100015",
      "agentName": "John Doe",
      "propertyType": "Industrial",
      "propertyAddress": "758 Pine Road, Riverside, NY",
      "propertyValue": 874856,
      "buyerName": "Dwayne Johnson",
      "buyerAddress": "537 Maple Avenue, Greenville, FL",
      "listingDate": "2024-04-25T00:00:00Z",
      "saleDate": "2024-07-11T00:00:00Z",
      "salePrice": 906202.9,
      "commission": 2.0,
      "commissionAmt": 18124.06
    },
    {
      "transactionId": "TXN100016",
      "agentName": "John Doe",
      "propertyType": "Commercial",
      "propertyAddress": "178 Oak Street, Springfield, NY",
      "propertyValue": 271237,
      "buyerName": "Chris Evans",
      "buyerAddress": "792 Pine Road, Centerville, FL",
      "listingDate": "2024-07-30T00:00:00Z",
      "saleDate": "2024-08-17T00:00:00Z",
      "salePrice": 277925.53,
      "commission": 2.0,
      "commissionAmt": 5558.51
    },
    {
      "transactionId": "TXN100017",
      "agentName": "John Doe",
      "propertyType": "Residential",
      "propertyAddress": "630 Oak Street, Centerville, FL",
      "propertyValue": 159462,
      "buyerName": "Natalie Portman",
      "buyerAddress": "267 Maple Avenue, Centerville, IL",
      "listingDate": "2024-08-30T00:00:00Z",
      "saleDate": "2024-09-19T00:00:00Z",
      "salePrice": 167289.41,
      "commission": 2.5,
      "commissionAmt": 4182.24
    },
    {
      "transactionId": "TXN100018",
      "agentName": "Jane Smith",
      "propertyType": "Industrial",
      "propertyAddress": "925 Elm Street, Greenville, IL",
      "propertyValue": 396934,
      "buyerName": "Chris Evans",
      "buyerAddress": "404 Maple Avenue, Riverside, TX",
      "listingDate": "2024-10-03T00:00:00Z",
      "saleDate": "2024-11-19T00:00:00Z",
      "salePrice": 382379.91,
      "commission": 2.5,
      "commissionAmt": 9559.5
    },
    {
      "transactionId": "TXN100019",
      "agentName": "Michael Lee",
      "propertyType": "Residential",
      "propertyAddress": "816 Main Street, Fairview, IL",
      "propertyValue": 469210,
      "buyerName": "Natalie Portman",
      "buyerAddress": "197 Oak Street, Greenville, FL",
      "listingDate": "2024-10-03T00:00:00Z",
      "saleDate": "2024-12-30T00:00:00Z",
      "salePrice": 476849.31,
      "commission": 3.0,
      "commissionAmt": 14305.48
    },
    {
      "transactionId": "TXN100020",
      "agentName": "Michael Lee",
      "propertyType": "Industrial",
      "propertyAddress": "965 Main Street, Fairview, FL",
      "propertyValue": 322268,
      "buyerName": "Natalie Portman",
      "buyerAddress": "922 Oak Street, Greenville, CA",
      "listingDate": "2024-10-03T00:00:00Z",
      "saleDate": "2024-12-11T00:00:00Z",
      "salePrice": 311520.01,
      "commission": 2.5,
      "commissionAmt": 7788.0
    },
    {
      "transactionId": "TXN100021",
      "agentName": "Jane Smith",
      "propertyType": "Industrial",
      "propertyAddress": "524 Pine Road, Fairview, IL",
      "propertyValue": 376859,
      "buyerName": "Emma Watson",
      "buyerAddress": "340 Oak Street, Riverside, FL",
      "listingDate": "2024-07-22T00:00:00Z",
      "saleDate": "2024-08-16T00:00:00Z",
      "salePrice": 381863.68,
      "commission": 2.5,
      "commissionAmt": 9546.59
    },
    {
      "transactionId": "TXN100022",
      "agentName": "Bob Brown",
      "propertyType": "Land",
      "propertyAddress": "390 Maple Avenue, Centerville, FL",
      "propertyValue": 327895,
      "buyerName": "Natalie Portman",
      "buyerAddress": "753 Maple Avenue, Springfield, CA",
      "listingDate": "2024-03-13T00:00:00Z",
      "saleDate": "2024-04-12T00:00:00Z",
      "salePrice": 334559.56,
      "commission": 3.0,
      "commissionAmt": 10036.79
    },
    {
      "transactionId": "TXN100023",
      "agentName": "John Doe",
      "propertyType": "Land",
      "propertyAddress": "723 Oak Street, Greenville, IL",
      "propertyValue": 258685,
      "buyerName": "Emma Watson",
      "buyerAddress": "236 Maple Avenue, Fairview, TX",
      "listingDate": "2024-01-08T00:00:00Z",
      "saleDate": "2024-04-06T00:00:00Z",
      "salePrice": 250202.62,
      "commission": 2.5,
      "commissionAmt": 6255.07
    },
    {
      "transactionId": "TXN100024",
      "agentName": "John Doe",
      "propertyType": "Industrial",
      "propertyAddress": "426 Elm Street, Fairview, TX",
      "propertyValue": 223825,
      "buyerName": "Tom Hanks",
      "buyerAddress": "538 Oak Street, Centerville, IL",
      "listingDate": "2024-04-18T00:00:00Z",
      "saleDate": "2024-06-09T00:00:00Z",
      "salePrice": 228604.69,
      "commission": 2.5,
      "commissionAmt": 5715.12
    },
    {
      "transactionId": "TXN100025",
      "agentName": "Bob Brown",
      "propertyType": "Commercial",
      "propertyAddress": "172 Maple Avenue, Springfield, IL",
      "propertyValue": 442307,
      "buyerName": "Tom Hanks",
      "buyerAddress": "945 Main Street, Riverside, IL",
      "listingDate": "2024-05-16T00:00:00Z",
      "saleDate": "2024-06-28T00:00:00Z",
      "salePrice": 440191.49,
      "commission": 3.0,
      "commissionAmt": 13205.74
    },
    {
      "transactionId": "TXN100026",
      "agentName": "Alice Johnson",
      "propertyType": "Industrial",
      "propertyAddress": "347 Pine Road, Greenville, CA",
      "propertyValue": 360293,
      "buyerName": "Tom Hanks",
      "buyerAddress": "965 Maple Avenue, Greenville, CA",
      "listingDate": "2024-10-11T00:00:00Z",
      "saleDate": "2024-12-06T00:00:00Z",
      "salePrice": 361195.73,
      "commission": 3.0,
      "commissionAmt": 10835.87
    },
    {
      "transactionId": "TXN100027",
      "agentName": "Michael Lee",
      "propertyType": "Industrial",
      "propertyAddress": "706 Pine Road, Centerville, IL",
      "propertyValue": 791644,
      "buyerName": "Emma Watson",
      "buyerAddress": "447 Maple Avenue, Centerville, NY",
      "listingDate": "2024-10-30T00:00:00Z",
      "saleDate": "2024-12-05T00:00:00Z",
      "salePrice": 853404.11,
      "commission": 2.5,
      "commissionAmt": 21335.1
    },
    {
      "transactionId": "TXN100028",
      "agentName": "Michael Lee",
      "propertyType": "Commercial",
      "propertyAddress": "804 Maple Avenue, Greenville, CA",
      "propertyValue": 921863,
      "buyerName": "Emma Watson",
      "buyerAddress": "747 Main Street, Fairview, NY",
      "listingDate": "2024-06-29T00:00:00Z",
      "saleDate": "2024-09-22T00:00:00Z",
      "salePrice": 946885.0,
      "commission": 2.0,
      "commissionAmt": 18937.7
    },
    {
      "transactionId": "TXN100029",
      "agentName": "Jane Smith",
      "propertyType": "Land",
      "propertyAddress": "293 Pine Road, Springfield, NY",
      "propertyValue": 826304,
      "buyerName": "Emma Watson",
      "buyerAddress": "223 Pine Road, Riverside, TX",
      "listingDate": "2024-02-03T00:00:00Z",
      "saleDate": "2024-03-11T00:00:00Z",
      "salePrice": 848005.56,
      "commission": 3.0,
      "commissionAmt": 25440.17
    },
    {
      "transactionId": "TXN100030",
      "agentName": "John Doe",
      "propertyType": "Residential",
      "propertyAddress": "172 Main Street, Springfield, CA",
      "propertyValue": 175079,
      "buyerName": "Natalie Portman",
      "buyerAddress": "575 Oak Street, Centerville, TX",
      "listingDate": "2024-06-29T00:00:00Z",
      "saleDate": "2024-08-25T00:00:00Z",
      "salePrice": 167423.3,
      "commission": 2.5,
      "commissionAmt": 4185.58
    },
    {
      "transactionId": "TXN100031",
      "agentName": "John Doe",
      "propertyType": "Residential",
      "propertyAddress": "119 Oak Street, Fairview, TX",
      "propertyValue": 564772,
      "buyerName": "Chris Evans",
      "buyerAddress": "895 Elm Street, Riverside, CA",
      "listingDate": "2024-04-10T00:00:00Z",
      "saleDate": "2024-05-14T00:00:00Z",
      "salePrice": 619094.67,
      "commission": 3.0,
      "commissionAmt": 18572.84
    },
    {
      "transactionId": "TXN100032",
      "agentName": "Bob Brown",
      "propertyType": "Residential",
      "propertyAddress": "144 Pine Road, Greenville, IL",
      "propertyValue": 250984,
      "buyerName": "Tom Hanks",
      "buyerAddress": "144 Maple Avenue, Greenville, FL",
      "listingDate": "2024-10-29T00:00:00Z",
      "saleDate": "2024-12-02T00:00:00Z",
      "salePrice": 243976.58,
      "commission": 3.5,
      "commissionAmt": 8539.18
    },
    {
      "transactionId": "TXN100033",
      "agentName": "Alice Johnson",
      "propertyType": "Residential",
      "propertyAddress": "858 Elm Street, Centerville, NY",
      "propertyValue": 750289,
      "buyerName": "Natalie Portman",
      "buyerAddress": "369 Pine Road, Centerville, IL",
      "listingDate": "2024-03-16T00:00:00Z",
      "saleDate": "2024-06-09T00:00:00Z",
      "salePrice": 727762.57,
      "commission": 2.0,
      "commissionAmt": 14555.25
    },
    {
      "transactionId": "TXN100034",
      "agentName": "Bob Brown",
      "propertyType": "Industrial",
      "propertyAddress": "173 Elm Street, Fairview, FL",
      "propertyValue": 127586,
      "buyerName": "Emma Watson",
      "buyerAddress": "637 Elm Street, Springfield, IL",
      "listingDate": "2024-03-01T00:00:00Z",
      "saleDate": "2024-05-06T00:00:00Z",
      "salePrice": 129638.22,
      "commission": 2.0,
      "commissionAmt": 2592.76
    },
    {
      "transactionId": "TXN100035",
      "agentName": "Alice Johnson",
      "propertyType": "Industrial",
      "propertyAddress": "660 Pine Road, Centerville, IL",
      "propertyValue": 109247,
      "buyerName": "Chris Evans",
      "buyerAddress": "475 Maple Avenue, Greenville, CA",
      "listingDate": "2024-09-09T00:00:00Z",
      "saleDate": "2024-09-22T00:00:00Z",
      "salePrice": 111157.8,
      "commission": 2.5,
      "commissionAmt": 2778.95
    },
    {
      "transactionId": "TXN100036",
      "agentName": "Alice Johnson",
      "propertyType": "Commercial",
      "propertyAddress": "803 Pine Road, Centerville, CA",
      "propertyValue": 202416,
      "buyerName": "Emma Watson",
      "buyerAddress": "263 Pine Road, Fairview, NY",
      "listingDate": "2024-06-01T00:00:00Z",
      "saleDate": "2024-08-09T00:00:00Z",
      "salePrice": 200041.15,
      "commission": 2.0,
      "commissionAmt": 4000.82
    },
    {
      "transactionId": "TXN100037",
      "agentName": "Jane Smith",
      "propertyType": "Residential",
      "propertyAddress": "233 Elm Street, Centerville, IL",
      "propertyValue": 484342,
      "buyerName": "Tom Hanks",
      "buyerAddress": "229 Maple Avenue, Fairview, FL",
      "listingDate": "2024-02-14T00:00:00Z",
      "saleDate": "2024-05-13T00:00:00Z",
      "salePrice": 461728.42,
      "commission": 3.0,
      "commissionAmt": 13851.85
    },
    {
      "transactionId": "TXN100038",
      "agentName": "Jane Smith",
      "propertyType": "Residential",
      "propertyAddress": "369 Elm Street, Fairview, CA",
      "propertyValue": 553723,
      "buyerName": "Tom Hanks",
      "buyerAddress": "852 Elm Street, Greenville, CA",
      "listingDate": "2024-04-26T00:00:00Z",
      "saleDate": "2024-06-12T00:00:00Z",
      "salePrice": 608853.62,
      "commission": 2.5,
      "commissionAmt": 15221.34
    },
    {
      "transactionId": "TXN100039",
      "agentName": "Jane Smith",
      "propertyType": "Commercial",
      "propertyAddress": "480 Pine Road, Riverside, IL",
      "propertyValue": 293896,
      "buyerName": "Dwayne Johnson",
      "buyerAddress": "534 Main Street, Greenville, TX",
      "listingDate": "2024-02-27T00:00:00Z",
      "saleDate": "2024-04-09T00:00:00Z",
      "salePrice": 302944.69,
      "commission": 3.0,
      "commissionAmt": 9088.34
    },
    {
      "transactionId": "TXN100040",
      "agentName": "Alice Johnson",
      "propertyType": "Commercial",
      "propertyAddress": "883 Elm Street, Springfield, IL",
      "propertyValue": 929344,
      "buyerName": "Emma Watson",
      "buyerAddress": "334 Maple Avenue, Greenville, FL",
      "listingDate": "2024-10-07T00:00:00Z",
      "saleDate": "2024-11-13T00:00:00Z",
      "salePrice": 977821.02,
      "commission": 3.5,
      "commissionAmt": 34223.74
    },
    {
      "transactionId": "TXN100041",
      "agentName": "Bob Brown",
      "propertyType": "Land",
      "propertyAddress": "789 Pine Road, Greenville, TX",
      "propertyValue": 560511,
      "buyerName": "Natalie Portman",
      "buyerAddress": "635 Main Street, Riverside, IL",
      "listingDate": "2024-06-08T00:00:00Z",
      "saleDate": "2024-07-31T00:00:00Z",
      "salePrice": 548437.14,
      "commission": 2.0,
      "commissionAmt": 10968.74
    },
    {
      "transactionId": "TXN100042",
      "agentName": "John Doe",
      "propertyType": "Industrial",
      "propertyAddress": "825 Main Street, Centerville, TX",
      "propertyValue": 101796,
      "buyerName": "Natalie Portman",
      "buyerAddress": "178 Oak Street, Springfield, NY",
      "listingDate": "2024-07-03T00:00:00Z",
      "saleDate": "2024-08-26T00:00:00Z",
      "salePrice": 107225.62,
      "commission": 3.5,
      "commissionAmt": 3752.9
    },
    {
      "transactionId": "TXN100043",
      "agentName": "Bob Brown",
      "propertyType": "Commercial",
      "propertyAddress": "380 Oak Street, Centerville, NY",
      "propertyValue": 136354,
      "buyerName": "Natalie Portman",
      "buyerAddress": "131 Pine Road, Centerville, FL",
      "listingDate": "2024-01-29T00:00:00Z",
      "saleDate": "2024-04-14T00:00:00Z",
      "salePrice": 130752.29,
      "commission": 3.0,
      "commissionAmt": 3922.57
    },
    {
      "transactionId": "TXN100044",
      "agentName": "John Doe",
      "propertyType": "Residential",
      "propertyAddress": "987 Oak Street, Greenville, FL",
      "propertyValue": 869335,
      "buyerName": "Dwayne Johnson",
      "buyerAddress": "167 Main Street, Fairview, FL",
      "listingDate": "2024-08-03T00:00:00Z",
      "saleDate": "2024-09-29T00:00:00Z",
      "salePrice": 867073.07,
      "commission": 3.5,
      "commissionAmt": 30347.56
    },
    {
      "transactionId": "TXN100045",
      "agentName": "Michael Lee",
      "propertyType": "Commercial",
      "propertyAddress": "222 Main Street, Fairview, CA",
      "propertyValue": 716346,
      "buyerName": "Chris Evans",
      "buyerAddress": "159 Main Street, Centerville, IL",
      "listingDate": "2024-07-27T00:00:00Z",
      "saleDate": "2024-09-12T00:00:00Z",
      "salePrice": 778693.03,
      "commission": 3.5,
      "commissionAmt": 27254.26
    },
    {
      "transactionId": "TXN100046",
      "agentName": "Michael Lee",
      "propertyType": "Residential",
      "propertyAddress": "601 Main Street, Centerville, IL",
      "propertyValue": 718738,
      "buyerName": "Natalie Portman",
      "buyerAddress": "569 Main Street, Riverside, NY",
      "listingDate": "2024-04-23T00:00:00Z",
      "saleDate": "2024-06-12T00:00:00Z",
      "salePrice": 715519.3,
      "commission": 3.0,
      "commissionAmt": 21465.58
    },
    {
      "transactionId": "TXN100047",
      "agentName": "Bob Brown",
      "propertyType": "Commercial",
      "propertyAddress": "225 Elm Street, Springfield, TX",
      "propertyValue": 525664,
      "buyerName": "Emma Watson",
      "buyerAddress": "453 Main Street, Greenville, CA",
      "listingDate": "2024-01-06T00:00:00Z",
      "saleDate": "2024-03-06T00:00:00Z",
      "salePrice": 529330.43,
      "commission": 3.0,
      "commissionAmt": 15879.91
    },
    {
      "transactionId": "TXN100048",
      "agentName": "Bob Brown",
      "propertyType": "Residential",
      "propertyAddress": "286 Pine Road, Riverside, IL",
      "propertyValue": 649149,
      "buyerName": "Natalie Portman",
      "buyerAddress": "313 Oak Street, Fairview, TX",
      "listingDate": "2024-09-12T00:00:00Z",
      "saleDate": "2024-12-08T00:00:00Z",
      "salePrice": 617329.33,
      "commission": 2.5,
      "commissionAmt": 15433.23
    },
    {
      "transactionId": "TXN100049",
      "agentName": "Michael Lee",
      "propertyType": "Residential",
      "propertyAddress": "549 Pine Road, Fairview, FL",
      "propertyValue": 712471,
      "buyerName": "Chris Evans",
      "buyerAddress": "911 Pine Road, Centerville, NY",
      "listingDate": "2024-07-20T00:00:00Z",
      "saleDate": "2024-08-21T00:00:00Z",
      "salePrice": 734228.24,
      "commission": 3.0,
      "commissionAmt": 22026.85
    }
  ]
}
''';
// Sample Brokers Data Response JSON
const String sampleBrokersDataResponse = '''
{
  "status": "success",
  "brokersData": [
    {
      "id": "1",
      "name": "Charlotte Anderson B1",
      "contact": "(415) 555-0101",
      "email": "<EMAIL>",
      "address": "123 Business Ave, San Francisco, CA",
      "totalAgents": 8,
      "totalSalesVolume": 15000.0,
      "totalSales": 15000.0,
      "totalCommission": 8000.0,
      "joinDate": "2022-03-15T00:00:00.000Z"
    },
    { 
      "id": "2",
      "name": "Benjamin Thomas B2",
      "contact": "(415) 555-0102",
      "email": "<EMAIL>",
      "address": "456 Commerce St, San Francisco, CA",
      "totalAgents": 3,
      "totalSalesVolume": 32000.0,
      "totalSales": 14000.0,
      "totalCommission": 12000.0,
      "joinDate": "2021-08-22T00:00:00.000Z"
    },
    {
      "id": "3",
      "name": "Mia Jackson B3",
      "contact": "(415) 555-0103",
      "email": "<EMAIL>",
      "address": "789 Market St, San Francisco, CA",
      "totalAgents": 6,
      "totalSalesVolume": 9000.0,
      "totalSales": 10000.0,
      "totalCommission": 3500.0,
      "joinDate": "2023-01-10T00:00:00.000Z"
    },
    {
      "id": "4",
      "name": "William White B4",
      "contact": "(415) 555-0104",
      "email": "<EMAIL>",
      "address": "321 Financial Blvd, San Francisco, CA",
      "totalAgents": 7,
      "totalSalesVolume": 25000.0,
      "totalSales": 50000.0,
      "totalCommission": 10000.0,
      "joinDate": "2022-11-05T00:00:00.000Z"
    },
    {
      "id": "5",
      "name": "Amelia Harris B5",
      "contact": "(415) 555-0105",
      "email": "<EMAIL>",
      "address": "654 Corporate Way, San Francisco, CA",
      "totalAgents": 6,
      "totalSalesVolume": 14000.0,
      "totalSales": 10000.0,
      "totalCommission": 6000.0,
      "joinDate": "2023-06-18T00:00:00.000Z"
    },
    {
      "id": "6",
      "name": "Lucas Martin B6",
      "contact": "(415) 555-0106",
      "email": "<EMAIL>",
      "address": "987 Enterprise Dr, San Francisco, CA",
      "totalAgents": 5,
      "totalSalesVolume": 11000.0,
      "totalSales": 10000.0,
      "totalCommission": 4500.0,
      "joinDate": "2023-02-28T00:00:00.000Z"
    }
  ]
}
''';
const String sampleAgentDataResponse = '''
{
  "status": "success",
  "agentData": [
    {
      "name": "Anita Mehra",
      "sales": 12,
      "amount": 150000.0,
      "commission": 7500.0,
      "contact": "+91 9876543210",
      "email": "<EMAIL>",
      "imageUrl": "https://example.com/agent1.jpg",
      "joinDate": "2022-01-15T00:00:00.000Z",
      "state": "Maharashtra",
      "city": "Mumbai",
      "level": "Senior",
      "totalDeals": 45,
      "earning": 22000.0,
      "status": true,
      "relatedBroker": "Elite Brokers",
      "totalAgents": 23,
      "soldHomes": 12,
      "totalSales": 150000
    },
    {
      "name": "Rohit Sharma",
      "sales": 18,
      "amount": 210000.0,
      "commission": 10500.0,
      "contact": "+91 9988776655",
      "email": "<EMAIL>",
      "imageUrl": "https://example.com/agent2.jpg",
      "joinDate": "2021-06-20T00:00:00.000Z",
      "state": "Delhi",
      "city": "New Delhi",
      "level": "Executive",
      "totalDeals": 60,
      "earning": 34000.0,
      "status": true,
      "relatedBroker": "Dream Homes",
      "totalAgents": 17,
      "soldHomes": 18,
      "totalSales": 210000
    },
    {
      "name": "Sneha Kapoor",
      "sales": 10,
      "amount": 120000.0,
      "commission": 6000.0,
      "contact": "+91 9123456780",
      "email": "<EMAIL>",
      "imageUrl": "https://example.com/agent3.jpg",
      "joinDate": "2020-11-05T00:00:00.000Z",
      "state": "Karnataka",
      "city": "Bangalore",
      "level": "Associate",
      "totalDeals": 38,
      "earning": 18000.0,
      "status": false,
      "relatedBroker": "Sunrise Realty",
      "totalAgents": 14,
      "soldHomes": 10,
      "totalSales": 120000
    },
    {
      "name": "Vikram Desai",
      "sales": 14,
      "amount": 175000.0,
      "commission": 8750.0,
      "contact": "+91 8899776655",
      "email": "<EMAIL>",
      "imageUrl": "https://example.com/agent4.jpg",
      "joinDate": "2019-09-12T00:00:00.000Z",
      "state": "Gujarat",
      "city": "Ahmedabad",
      "level": "Senior",
      "totalDeals": 52,
      "earning": 27000.0,
      "status": true,
      "relatedBroker": "Elite Brokers",
      "totalAgents": 28,
      "soldHomes": 14,
      "totalSales": 175000
    },
    {
      "name": "Neha Reddy",
      "sales": 8,
      "amount": 95000.0,
      "commission": 4750.0,
      "contact": "+91 7766554433",
      "email": "<EMAIL>",
      "imageUrl": "https://example.com/agent5.jpg",
      "joinDate": "2023-02-10T00:00:00.000Z",
      "state": "Telangana",
      "city": "Hyderabad",
      "level": "Junior",
      "totalDeals": 25,
      "earning": 12000.0,
      "status": true,
      "relatedBroker": "Dream Homes",
      "totalAgents": 11,
      "soldHomes": 8,
      "totalSales": 95000
    },
    {
      "name": "Arjun Patel",
      "sales": 11,
      "amount": 130000.0,
      "commission": 6500.0,
      "contact": "+91 9090909090",
      "email": "<EMAIL>",
      "imageUrl": "https://example.com/agent6.jpg",
      "joinDate": "2021-07-22T00:00:00.000Z",
      "state": "Rajasthan",
      "city": "Jaipur",
      "level": "Executive",
      "totalDeals": 42,
      "earning": 19500.0,
      "status": true,
      "relatedBroker": "Sunrise Realty",
      "totalAgents": 19,
      "soldHomes": 11,
      "totalSales": 130000
    },
    {
      "name": "Divya Sinha",
      "sales": 16,
      "amount": 180000.0,
      "commission": 9000.0,
      "contact": "+91 8822446688",
      "email": "<EMAIL>",
      "imageUrl": "https://example.com/agent7.jpg",
      "joinDate": "2022-08-30T00:00:00.000Z",
      "state": "West Bengal",
      "city": "Kolkata",
      "level": "Senior",
      "totalDeals": 48,
      "earning": 25000.0,
      "status": true,
      "relatedBroker": "Elite Brokers",
      "totalAgents": 25,
      "soldHomes": 16,
      "totalSales": 180000
    }
   
  ]
}
''';
