import 'dart:developer' as developer;

import 'package:flutter/foundation.dart';

import '../../core/network/dio_client.dart';
import '../../core/services/api_error_handler.dart';
import '/src/core/config/app_strings.dart';
import '/src/domain/models/login.dart';
import '../../core/network/api_config.dart' show APIConfig;
import '../../domain/repository/auth_repository.dart';
import '../../core/services/exceptions.dart';
import '../../core/network/api_config.dart';
import '../../core/services/firebase_auth_service.dart';
import '../../domain/models/google_auth_result.dart';
import 'package:dio/dio.dart';

class AuthRepositoryImpl extends AuthRepository {
  AuthRepositoryImpl();

  static final String baseUrl = APIConfig.baseUrl;
  static const String loginUrl = APIConfig.login;
  static const String createPasswordUrl = APIConfig.createPassword;
  static const String socialSignInUrl = APIConfig.socialSignIn;
  final FirebaseAuthService _firebaseAuthService = FirebaseAuthService();

  @override
  Future<dynamic> login(Map<String, dynamic> payload) async {
    try {
      final _dio = await DioClient.getDio();
      final response = await _dio.post(loginUrl, data: payload);

      if (response.statusCode == 200) {
        return LoginModel.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, loginFailed);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  ///v1/auth/password

  @override
  Future<dynamic> createPassword(Map<String, dynamic> payload) async {
    try {
      final dio = await DioClient.getDio();
      final response = await dio.post(createPasswordUrl, data: payload);

      if (response.statusCode == 200) {
        final message = response.data['message'] ?? '';
        return message;
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } on DioException catch (e) {
      throw ApiErrorHandler.handleDioException(e, loginFailed);
    } catch (e) {
      throw ApiException(message: e.toString(), statusCode: 500);
    }
  }

  @override
  Future<dynamic> signInWithGoogle() async {
    try {
      final result = await _firebaseAuthService.signInWithGoogle();
      final _dio = await DioClient.getDio();
      final payload = {'idToken': result.idToken};
      final response = await _dio.post(socialSignInUrl, data: payload);
      if (response.statusCode == 200) {
        return LoginModel.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } catch (e, stackTrace) {
      return GoogleAuthResult.failure(e.toString());
    }
  }

  @override
  Future<dynamic> signInWithApple() async {
    try {
      final result = await _firebaseAuthService.signInWithApple();
      final _dio = await DioClient.getDio();
      final payload = {'idToken': result.idToken};
      final response = await _dio.post(socialSignInUrl, data: payload);
      if (response.statusCode == 200) {
        return LoginModel.fromJson(response.data);
      } else {
        throw ApiErrorHandler.handleResponseError(
          response.statusCode,
          response.data,
        );
      }
    } catch (e) {
      return GoogleAuthResult.failure(e.toString());
    }
  }
}
